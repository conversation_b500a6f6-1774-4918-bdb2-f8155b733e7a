"""
CogBridges Search - 核心业务流程服务
实现完整的串行业务流程：Grok（仅搜索，返回 citations）-> Reddit API 抓取帖子/评论 -> 评论者历史数据获取 -> LLM分析
"""

import asyncio
import time
from typing import List, Dict, Any, Optional
from datetime import datetime
from dataclasses import dataclass, field

from config import config
from services.reddit_service import RedditService
from services.data_service import DataService
from services.llm_service import llm_service

from models.search_models import SearchQuery, SearchResult
from utils.logger_utils import get_logger
# from utils.performance_monitor import performance_monitor, PerformanceContext


@dataclass
class CogBridgesSearchResult:
    """CogBridges搜索结果"""
    query: str  # 原始查询
    session_id: str
    timestamp: datetime = field(default_factory=datetime.now)
    
    # 查询信息
    translated_query: str = ""  # 查询字符串
    translation_time: float = 0.0  # 查询处理时间
    
    # 步骤1: Grok搜索结果（保持字段名兼容性）
    google_results: List[Dict[str, Any]] = field(default_factory=list)
    google_search_time: float = 0.0
    
    # 步骤1: Reddit帖子数据（由Reddit API基于GroK citations抓取）
    reddit_posts: List[Dict[str, Any]] = field(default_factory=list)
    reddit_posts_time: float = 0.0
    
    # 步骤2: 评论者历史数据
    commenters_history: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    commenters_history_time: float = 0.0
    
    # 步骤3: LLM分析结果
    llm_analysis: Dict[str, Any] = field(default_factory=dict)
    llm_analysis_time: float = 0.0
    
    # 总体统计
    total_time: float = 0.0
    success: bool = True
    error_message: str = ""
    # 归属用户
    owner_user_id: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "query": self.query,
            "translated_query": self.translated_query,
            "session_id": self.session_id,
            "timestamp": self.timestamp.isoformat(),
            "translation_time": self.translation_time,
            "google_results": self.google_results,
            "google_search_time": self.google_search_time,
            "reddit_posts": self.reddit_posts,
            "reddit_posts_time": self.reddit_posts_time,
            "commenters_history": self.commenters_history,
            "commenters_history_time": self.commenters_history_time,
            "llm_analysis": self.llm_analysis,
            "llm_analysis_time": self.llm_analysis_time,
            "total_time": self.total_time,
            "success": self.success,
            "error_message": self.error_message,
            "owner_user_id": self.owner_user_id
        }


class CogBridgesService:
    """CogBridges核心业务服务"""
    
    def __init__(self):
        """初始化服务"""
        self.logger = get_logger(__name__)
        

        self.reddit_service = RedditService()  # 直接实例化
        self.data_service = DataService()
        self.llm_service = llm_service

        # 初始化Grok Reddit服务
        from .grok_reddit_service import GrokRedditService
        self.grok_service = GrokRedditService()

        
        # 业务参数（可配置）
        self.max_search_results = 5  # 前5个搜索结果
        self.max_comments_per_post = 6  # 每个帖子前6个评论
        self.max_user_comments = 20  # 用户历史前20个评论
        self.max_user_posts = 10  # 用户历史前10个帖子
        
        self.logger.info("CogBridges核心业务服务初始化成功")



    async def close(self):
        """关闭服务并清理资源"""
        await self.reddit_service.close()
        await self.grok_service.close()
        self.logger.info("CogBridges服务已关闭")
    
    async def search(self, query: str, save_to_db: bool = True, cancel_event=None) -> CogBridgesSearchResult:
        """
        执行完整的CogBridges搜索流程

        Args:
            query: 搜索查询
            save_to_db: 是否保存到数据库，默认为True

        Returns:
            完整的搜索结果
        """
        start_time = time.time()
        session_id = self.data_service.generate_session_id(query)
        
        result = CogBridgesSearchResult(
            query=query,
            session_id=session_id
        )
        
        try:
            self.logger.info(f"开始CogBridges搜索流程: {query}")
            if cancel_event is not None and getattr(cancel_event, 'is_set', lambda: False)():
                result.success = False
                result.error_message = 'cancelled'
                return result
            
            # 直接使用原始查询
            optimized_query = query
            
            # 保存查询信息到结果中
            result.translated_query = optimized_query  # 使用原始查询
            result.translation_time = 0.0  # 无优化处理时间
            
            # 步骤1: 使用Grok API进行Reddit搜索
            if cancel_event is not None and cancel_event.is_set():
                result.success = False
                result.error_message = 'cancelled'
                return result
            grok_results = await self._step1_grok_reddit_search(optimized_query)
            result.google_results = grok_results.get("google_results", [])
            result.google_search_time = grok_results["search_time"]
            result.reddit_posts = grok_results["posts"]
            result.reddit_posts_time = grok_results["search_time"]  # Grok一次性完成搜索和数据获取

            if not result.reddit_posts:
                result.success = False
                result.error_message = grok_results.get("error", "Grok Reddit搜索未找到结果")
                return result

            # 步骤2: 并行获取评论者历史数据
            if cancel_event is not None and cancel_event.is_set():
                result.success = False
                result.error_message = 'cancelled'
                return result
            commenters_data = await self._step2_get_commenters_history(result.reddit_posts, cancel_event=cancel_event)
            result.commenters_history = commenters_data["history"]
            result.commenters_history_time = commenters_data["processing_time"]

            # 对齐展示集合：仅保留那些已拉取历史的作者的评论，保证每条展示评论都有对应的comment card
            try:
                processed_usernames = set(commenters_data.get("selected_usernames", list(result.commenters_history.keys())))
                pruned_posts: List[Dict[str, Any]] = []
                for entry in (result.reddit_posts or []):
                    post = entry.get("post", {}) if isinstance(entry, dict) else {}
                    kept_comments = []
                    kept_commenters = []
                    for c in (entry.get("comments", []) or []):
                        author = c.get("author")
                        if author and author in processed_usernames:
                            kept_comments.append(c)
                            if author not in kept_commenters:
                                kept_commenters.append(author)
                    if kept_comments:
                        pruned_posts.append({
                            "success": True,
                            "post": post,
                            "comments": kept_comments,
                            "commenters": kept_commenters,
                        })
                result.reddit_posts = pruned_posts
            except Exception as align_err:
                self.logger.warning(f"对齐展示评论与已拉取历史的作者失败，保留原集合: {align_err}")
            
            # 步骤3: LLM分析（如果LLM服务可用且有评论者数据）
            llm_analysis_results = None
            llm_analysis_time = 0.0
            
            if (self.llm_service and self.llm_service.configured and 
                result.commenters_history and len(result.commenters_history) > 0):
                try:
                    self.logger.info(f"步骤3: 开始LLM分析...")
                    llm_analysis_start = time.time()
                    
                    # 执行LLM分析
                    if cancel_event is not None and cancel_event.is_set():
                        raise Exception('cancelled')
                    llm_analysis_results = await self._step3_llm_analysis(result, cancel_event=cancel_event)
                    
                    llm_analysis_time = time.time() - llm_analysis_start
                    self.logger.info(f"步骤3完成: LLM分析完成, 耗时: {llm_analysis_time:.2f}秒")
                    
                except Exception as e:
                    self.logger.warning(f"LLM分析失败: {e}")
                    llm_analysis_results = {"error": str(e)}
            
            # 保存LLM分析结果
            result.llm_analysis = llm_analysis_results or {}
            result.llm_analysis_time = llm_analysis_time
            
            # 计算总时间
            result.total_time = time.time() - start_time

            # 根据参数决定是否保存完整结果
            if save_to_db:
                await self._save_results(result)

            self.logger.info(f"CogBridges搜索完成: {query}, 总耗时: {result.total_time:.2f}秒")
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
            result.total_time = time.time() - start_time
            self.logger.error(f"CogBridges搜索失败: {e}")
        
        return result

    async def save_search_result_async(self, result: CogBridgesSearchResult):
        """
        异步保存搜索结果到数据库

        Args:
            result: 搜索结果对象
        """
        try:
            await self._save_results(result)
            self.logger.info(f"搜索结果异步保存成功: {result.session_id}")
        except Exception as e:
            self.logger.error(f"搜索结果异步保存失败: {e}")


    async def _step1_grok_reddit_search(self, query: str, cancel_event=None) -> Dict[str, Any]:
        """步骤1: 使用Grok API进行Reddit搜索"""
        self.logger.info(f"步骤1: Grok 搜索(仅搜索) - {query}")
        start_time = time.time()

        # 使用Grok服务搜索Reddit（仅搜索，依赖 citations 获取真实链接）
        # 若已取消，尽早返回
        if cancel_event is not None and cancel_event.is_set():
            return {"posts": [], "google_results": [], "search_time": time.time() - start_time, "error": "cancelled"}

        grok_result = await self.grok_service.search_reddit(
            query=query,
            search_only=True,
            cancel_event=cancel_event,
        )

        if not grok_result["success"]:
            self.logger.warning(f"Grok Reddit搜索失败: {grok_result['error']}")
            return {
                "posts": [],
                "google_results": [],
                "search_time": grok_result["search_time"],
                "error": grok_result["error"]
            }

        # 基于 citations 抓取真实 Reddit 帖子与评论
        citations = grok_result.get("citations", []) or []
        if cancel_event is not None and cancel_event.is_set():
            return {"posts": [], "google_results": [], "search_time": grok_result["search_time"], "error": "cancelled"}
        converted_posts = await self._fetch_posts_from_citations(citations, cancel_event=cancel_event)

        # 接入内容筛选：使用 Replicate 的 GPT-5 系列模型选出与查询最相关的评论
        try:
            from services.content_selection_service import ContentSelectionService
            # 业务固定启用内容筛选流程
            selector = ContentSelectionService()
            converted_posts = await selector.select_relevant_comments(
                converted_posts,
                query=query,
                max_comments=config.CONTENT_SELECTION_MAX_COMMENTS,
            )
        except Exception as sel_err:
            self.logger.warning(f"内容筛选失败，使用原始评论集合: {sel_err}")

        # 兜底裁剪：若筛选失败或结果超过上限，则按分数进行确定性裁剪，确保后续只拉取必要的用户历史
        try:
            max_total = int(getattr(config, 'CONTENT_SELECTION_MAX_COMMENTS', 10))
            max_per_post = int(getattr(config, 'CONTENT_SELECTION_MAX_PER_POST', 5))
            total_after = sum(len(p.get('comments', []) or []) for p in converted_posts)
            if total_after == 0:
                self.logger.info("筛选后无评论，跳过兜底裁剪")
            elif total_after > max_total:
                flat = []
                for pi, p in enumerate(converted_posts):
                    post = p.get('post', {})
                    for ci, c in enumerate((p.get('comments', []) or [])):
                        flat.append({
                            'pi': pi,
                            'ci': ci,  # 记录评论在帖子中的原始索引
                            'score': c.get('score', 0) or 0,
                            'relevance_score': c.get('_relevance_score', 0),
                            'comment': c,
                            'post': post,
                        })
                # 优先按相关性分数排序，如果没有相关性分数则按Reddit分数排序
                flat.sort(key=lambda x: (x.get('relevance_score', 0), x.get('score', 0)), reverse=True)
                kept_keys = []
                kept_per_post = {}
                new_posts = []
                # 先复制空帖子壳
                for p in converted_posts:
                    new_posts.append({
                        'success': True,
                        'post': p.get('post', {}),
                        'comments': [],
                        'commenters': []
                    })
                # 记录每个帖子要保留的评论及其原始索引
                post_comments_with_index = {}
                for pi in range(len(converted_posts)):
                    post_comments_with_index[pi] = []
                    
                for item in flat:
                    if len(kept_keys) >= max_total:
                        break
                    pi = item['pi']
                    cnt = kept_per_post.get(pi, 0)
                    if cnt >= max_per_post:
                        continue
                    c = item['comment']
                    ci = item['ci']
                    post_comments_with_index[pi].append((ci, c))
                    kept_keys.append(c.get('permalink') or c.get('id'))
                    kept_per_post[pi] = cnt + 1
                    
                # 对每个帖子，按原始索引排序评论以保持顺序
                for pi, comments_with_index in post_comments_with_index.items():
                    # 按原始索引排序
                    comments_with_index.sort(key=lambda x: x[0])
                    for _, c in comments_with_index:
                        new_posts[pi]['comments'].append(c)
                        author = c.get('author')
                        if author and author != '[deleted]' and author not in new_posts[pi]['commenters']:
                            new_posts[pi]['commenters'].append(author)
                            
                # 去除无评论的帖子
                converted_posts = [p for p in new_posts if p.get('comments')]
                self.logger.info(f"筛选兜底裁剪生效: {total_after} -> {sum(len(p.get('comments', [])) for p in converted_posts)} 条")
        except Exception as prune_err:
            self.logger.warning(f"筛选兜底裁剪失败，使用筛选结果: {prune_err}")

        # （已按需将标准化与筛选逻辑移出此模块）

        search_time = time.time() - start_time
        self.logger.info(f"步骤1完成: citations 抓取并构建 {len(converted_posts)} 个帖子，耗时: {search_time:.2f}秒")

        return {
            "posts": converted_posts,
            "google_results": [],
            "search_time": search_time,
            "citations": citations  # 传递搜索引用
        }

    async def _fetch_posts_from_citations(self, citations: list, cancel_event=None) -> List[Dict[str, Any]]:
        """根据 Grok 返回的 citations（reddit.com 链接）抓取真实帖子与评论
        - 仅保留 reddit.com 域名
        - 对每个帖子的 URL 用 Reddit API 获取帖子详情与前若干条顶级评论
        - 统一转换为内部 posts 数据结构
        """
        from urllib.parse import urlparse, urlunparse
        import asyncio

        posts: List[Dict[str, Any]] = []
        if not citations:
            return posts

        # 过滤出 reddit 链接
        reddit_urls: List[str] = []
        for c in citations:
            try:
                url = None
                if isinstance(c, dict):
                    url = c.get("url") or c.get("uri")
                elif isinstance(c, str):
                    url = c
                if not url:
                    continue
                parsed = urlparse(url)
                host = (parsed.netloc or '').lower()
                if 'reddit.com' not in host:
                    continue
                # 只收集帖子链接（含 /comments/）
                if '/comments/' in (parsed.path or ''):
                    reddit_urls.append(url)
            except Exception:
                continue

        # 规范化与去重：
        # - 仅按帖子ID去重（/r/{sub}/comments/{post_id}），忽略标题段、评论段、查询参数与片段
        # - 统一域名为 reddit.com，协议为 https
        import re
        seen_posts = set()  # 存储 canonical key: /r/{sub}/comments/{post_id}
        unique_urls = []    # 对应的规范化URL: https://reddit.com/r/{sub}/comments/{post_id}
        post_pattern = re.compile(r"/r/([^/]+)/comments/([^/]+)", re.IGNORECASE)
        for u in reddit_urls:
            try:
                p = urlparse(u)
                raw_path = (p.path or "").strip()
                if not raw_path:
                    continue
                # 统一小写并去尾斜杠，仅用于匹配
                path_lower = raw_path.lower()
                if len(path_lower) > 1 and path_lower.endswith('/'):
                    path_lower = path_lower[:-1]
                # 匹配帖子ID
                m = post_pattern.search(path_lower)
                if not m:
                    continue
                subreddit = m.group(1)
                post_id = m.group(2)
                canonical_path = f"/r/{subreddit}/comments/{post_id}"
                if canonical_path in seen_posts:
                    continue
                seen_posts.add(canonical_path)
                clean_url = urlunparse((
                    'https',
                    'reddit.com',
                    canonical_path,
                    '',  # params
                    '',  # query（移除 tl/utm/context 等所有参数）
                    ''   # fragment
                ))
                unique_urls.append(clean_url)
            except Exception:
                # 解析失败时尽量不丢数据，但仍按原URL做一次最小化去重
                if u not in seen_posts:
                    # 使用原URL的去重键（低保真）
                    seen_posts.add(u)
                    unique_urls.append(u)

        if not unique_urls:
            return posts

        # 无 Reddit API 配置时，返回最小结构，避免中断
        if not self.reddit_service or not config.reddit_configured:
            for url in unique_urls:
                posts.append({
                    "success": True,
                    "post": {
                        "id": url.split('/')[-2] if '/' in url else "unknown",
                        "title": "",
                        "selftext": "",
                        "url": url,
                        "permalink": self._extract_permalink_from_url(url),
                        "author": "",
                        "subreddit": "",
                        "score": 0,
                        "num_comments": 0,
                        "created_utc": None
                    },
                    "comments": [],
                    "commenters": []
                })
            return posts

        # 使用 Reddit API 并发抓取帖子和评论
        # 并发度与每帖评论数可通过配置控制
        concurrency = min(getattr(config, 'REDDIT_POST_FETCH_CONCURRENCY', 6), len(unique_urls))
        semaphore = asyncio.Semaphore(concurrency)
        comments_limit = getattr(config, 'REDDIT_TOP_COMMENTS_COUNT', 6)

        async def fetch_one(url: str) -> Dict[str, Any]:
            async with semaphore:
                try:
                    if cancel_event is not None and cancel_event.is_set():
                        return {
                            "success": False,
                            "post": {"id": "cancelled", "title": "", "selftext": "", "url": url, "permalink": self._extract_permalink_from_url(url), "author": "", "subreddit": "", "score": 0, "num_comments": 0, "created_utc": None},
                            "comments": [],
                            "commenters": []
                        }
                    # 并行抓取帖子详情与评论
                    post_details_task = self.reddit_service.get_post_details(url)
                    comments_task = self.reddit_service.get_post_comments(url, limit=comments_limit)
                    post_details, comments = await asyncio.gather(post_details_task, comments_task)
                    post = {
                        "id": (post_details or {}).get("id", url.split('/')[-2] if '/' in url else "unknown"),
                        "title": (post_details or {}).get("title", ""),
                        "selftext": (post_details or {}).get("selftext", ""),
                        "url": url,
                        "permalink": self._extract_permalink_from_url(url),
                        "author": (post_details or {}).get("author", ""),
                        "subreddit": (post_details or {}).get("subreddit", ""),
                        "score": (post_details or {}).get("score", 0),
                        "num_comments": (post_details or {}).get("num_comments", 0),
                        "created_utc": (post_details or {}).get("created_utc", None)
                    }

                    normalized_comments = []
                    commenters = []
                    for c in comments or []:
                        item = {
                            "id": c.get("id"),
                            "body": c.get("body"),
                            "author": c.get("author"),
                            "score": c.get("score", 0),
                            "created_utc": c.get("created_utc"),
                            "permalink": c.get("permalink")
                        }
                        normalized_comments.append(item)
                        if item.get("author") and item["author"] != "[deleted]":
                            commenters.append(item["author"])

                    return {
                        "success": True,
                        "post": post,
                        "comments": normalized_comments,
                        "commenters": commenters
                    }
                except Exception as e:
                    self.logger.warning(f"抓取帖子失败: {url} - {e}")
                    return {
                        "success": False,
                        "post": {
                            "id": url.split('/')[-2] if '/' in url else "unknown",
                            "title": "",
                            "selftext": "",
                            "url": url,
                            "permalink": self._extract_permalink_from_url(url),
                            "author": "",
                            "subreddit": "",
                            "score": 0,
                            "num_comments": 0,
                            "created_utc": None
                        },
                        "comments": [],
                        "commenters": []
                    }

        results = await asyncio.gather(*[fetch_one(u) for u in unique_urls], return_exceptions=False)
        # 仅保留成功或最小结构
        for r in results:
            if isinstance(r, dict):
                posts.append(r)
        return posts

    

    def _convert_grok_results_to_cogbridges_format(self, grok_result) -> List[Dict[str, Any]]:
        """将Grok搜索结果转换为CogBridges格式"""
        converted_posts = []

        # 为每个帖子创建一个条目，包含其评论
        posts_with_comments = {}

        # 首先处理所有帖子
        for post in grok_result.posts:
            # 从URL中提取permalink路径
            permalink = self._extract_permalink_from_url(post.url)
            
            post_data = {
                "success": True,
                "post": {
                    "id": post.url.split('/')[-2] if '/' in post.url else "unknown",
                    "title": post.title,
                    "selftext": post.selftext,
                    "url": post.url,
                    "permalink": permalink,
                    "author": post.author,
                    "subreddit": post.subreddit,
                    "score": post.score,
                    "num_comments": post.num_comments,
                    "created_utc": post.created_utc
                },
                "comments": [],
                "commenters": []
            }
            posts_with_comments[post.url] = post_data

        # 然后将评论分配到对应的帖子
        for comment in grok_result.comments:
            comment_data = {
                "id": f"comment_{hash(comment.body)}",
                "body": comment.body,
                "author": comment.author,
                "score": comment.score,
                "created_utc": comment.created_utc,
                "permalink": comment.permalink
            }

            # 根据评论的permalink找到对应的帖子
            assigned_to_post = False
            comment_post_path = comment.permalink.split('/comments/')[0] if '/comments/' in comment.permalink else None
            
            if comment_post_path:
                for post_url, post_data in posts_with_comments.items():
                    post_path = post_data["post"]["permalink"].split('/comments/')[0] if '/comments/' in post_data["post"]["permalink"] else None
                    if comment_post_path == post_path:
                        post_data["comments"].append(comment_data)
                        if comment.author != "[deleted]":
                            post_data["commenters"].append(comment.author)
                        assigned_to_post = True
                        break
            
            # 如果没有找到对应的帖子，创建一个虚拟帖子来承载这个评论
            if not assigned_to_post:
                virtual_post = {
                    "success": True,
                    "post": {
                        "id": f"virtual_{hash(comment.body)}",
                        "title": f"Reddit discussion about {grok_result.search_query}",
                        "selftext": "",
                        "url": comment.author_url,
                        "permalink": comment.permalink.split('/comment/')[0] if '/comment/' in comment.permalink else comment.permalink,
                        "author": "various",
                        "subreddit": "various",
                        "score": 0,
                        "num_comments": 1,
                        "created_utc": comment.created_utc
                    },
                    "comments": [comment_data],
                    "commenters": [comment.author] if comment.author != "[deleted]" else []
                }
                converted_posts.append(virtual_post)

        # 添加所有有帖子数据的条目
        converted_posts.extend(posts_with_comments.values())

        return converted_posts

    def _extract_permalink_from_url(self, url: str) -> str:
        """从Reddit URL中提取permalink路径"""
        try:
            from urllib.parse import urlparse
            import re
            
            parsed = urlparse(url)
            path = parsed.path
            
            # 匹配Reddit帖子URL: /r/subreddit/comments/post_id/title/
            post_pattern = r'/r/([^/]+)/comments/([^/]+)'
            match = re.search(post_pattern, path)
            if match:
                # 返回完整的路径部分
                return path
            
            # 如果不是标准格式，返回路径部分
            return path if path else "/"
            
        except Exception as e:
            self.logger.error(f"提取permalink失败: {e}")
            return "/"

    
    async def _step2_get_commenters_history(self, posts_data: List[Dict[str, Any]], cancel_event=None) -> Dict[str, Any]:
        """步骤2: 使用新的overview获取方法获取评论者历史数据"""
        self.logger.info("步骤2: 使用新的overview方法获取评论者历史数据")
        start_time = time.time()

        # 收集评论者（仅来自当前筛选后的展示评论；保留出现顺序）
        ordered_commenters: List[str] = []
        seen = set()
        for post_data in posts_data:
            for c in (post_data.get("comments", []) or []):
                u = c.get("author")
                if u and u not in seen and u != "[deleted]":
                    ordered_commenters.append(u)
                    seen.add(u)
        all_commenters = set(ordered_commenters)

        # 过滤有效评论者（不再按人数上限截断，展示集合由筛选结果限定）
        from utils.reddit_utils import filter_valid_commenters
        filtered_by_valid = filter_valid_commenters(list(ordered_commenters))
        filtered_commenters = filtered_by_valid
        self.logger.info(f"需要获取 {len(filtered_commenters)} 个评论者的历史数据 (原始: {len(all_commenters)})")

        # 使用新的overview获取方法（带全局重试）
        commenters_history = {}
        max_items_per_user = max(10, int(getattr(config, 'USER_OVERVIEW_MAX_ITEMS', 80)))
        overview_concurrency = max(1, int(getattr(config, 'COMMENTERS_HISTORY_CONCURRENCY', 8)))
        # 全局重试逻辑移除：单次尝试
        max_global_retries = 0
        min_success_threshold = 1
        base_backoff_sec = 0

        async def run_batch(usernames: List[str]) -> Dict[str, Any]:
            sem = asyncio.Semaphore(min(overview_concurrency, max(1, len(usernames))))
            async def fetch_user_overview(username):
                async with sem:
                    if cancel_event is not None and cancel_event.is_set():
                        raise Exception('cancelled')
                    return await self.reddit_service.get_user_full_overview_history(username, max_items_per_user)
            tasks = [fetch_user_overview(u) for u in usernames]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            parsed: Dict[str, Any] = {}
            for i, r in enumerate(batch_results):
                uname = usernames[i]
                if isinstance(r, dict) and r.get('status') == 'success':
                    from utils.reddit_utils import convert_overview_to_legacy_format
                    parsed[uname] = convert_overview_to_legacy_format(r)
                elif isinstance(r, Exception):
                    self.logger.warning(f"获取用户 {uname} 历史失败: {r}")
            return parsed

        # 单次批量执行
        remaining_usernames: List[str] = list(filtered_commenters)
        if cancel_event is not None and cancel_event.is_set():
            return {"history": {}, "processing_time": time.time() - start_time, "selected_usernames": []}
        self.logger.info(f"步骤2批次获取：单次尝试，用户数 {len(remaining_usernames)}")
        batch_ok = await run_batch(remaining_usernames)
        for uname, data in batch_ok.items():
            if uname not in commenters_history:
                commenters_history[uname] = data

        processing_time = time.time() - start_time
        self.logger.info(f"步骤2完成: 获取了 {len(commenters_history)} 个用户的历史数据, 单次尝试, 耗时: {processing_time:.2f}秒")

        return {
            "history": commenters_history,
            "processing_time": processing_time,
            "selected_usernames": filtered_commenters
        }
    

    
    async def _save_results(self, result: CogBridgesSearchResult):
        """保存完整的搜索结果"""
        try:
            # 构建完整的会话数据
            # 构建 reddit_data 以便数据库路径能持久化帖子与评论
            reddit_posts_flat = []
            reddit_comments_flat = []
            user_histories = {}
            try:
                for entry in (result.reddit_posts or []):
                    post_obj = (entry or {}).get("post", {})
                    if isinstance(post_obj, dict) and post_obj.get("id"):
                        reddit_posts_flat.append({
                            "id": post_obj.get("id", ""),
                            "title": post_obj.get("title", ""),
                            "selftext": post_obj.get("selftext", ""),
                            "author": post_obj.get("author", ""),
                            "score": post_obj.get("score", 0),
                            "num_comments": post_obj.get("num_comments", 0),
                            "created_utc": post_obj.get("created_utc", 0),
                            "subreddit": post_obj.get("subreddit", ""),
                            "permalink": post_obj.get("permalink", ""),
                            "url": post_obj.get("url", ""),
                        })
                        for c in (entry.get("comments", []) or []):
                            if not isinstance(c, dict):
                                continue
                            reddit_comments_flat.append({
                                "id": c.get("id", ""),
                                "post_id": post_obj.get("id"),
                                "body": c.get("body", ""),
                                "author": c.get("author", ""),
                                "score": c.get("score", 0),
                                "created_utc": c.get("created_utc", 0),
                                "parent_id": c.get("parent_id"),
                                "subreddit": post_obj.get("subreddit", ""),
                                "permalink": c.get("permalink", ""),
                                "relevance_score": c.get("_relevance_score", 0.0),  # 添加相关性分数
                            })
                
                # 转换commenters_history为user_histories格式
                if result.commenters_history:
                    user_histories = result.commenters_history
                    
            except Exception as _rd_err:
                self.logger.warning(f"构建reddit_data失败，继续保存raw: {_rd_err}")

            complete_data = {
                "session_id": result.session_id,
                "timestamp": result.timestamp.isoformat(),
                "search_result": result.to_dict(),
                "metadata": {
                    "service_version": "2.0.0",
                                    "features_enabled": {
                    "llm_analysis": bool(result.llm_analysis),
                    "enhanced_comments": True
                }
                },
                "statistics": {
                    "google_results_count": len(result.google_results) if result.google_results else 0,
                    "reddit_posts_count": len(result.reddit_posts) if result.reddit_posts else 0,
                    "commenters_count": len(result.commenters_history) if result.commenters_history else 0,
                    "similarity_analysis_count": len(result.llm_analysis.get("similarity_analysis", {})) if result.llm_analysis else 0,
                    "motivation_analysis_count": sum(len(motivations) for motivations in result.llm_analysis.get("motivation_analysis", {}).values()) if result.llm_analysis else 0,
                    "translation_time": result.translation_time,
                    "google_search_time": result.google_search_time,
                    "reddit_posts_time": result.reddit_posts_time,
                    "commenters_history_time": result.commenters_history_time,
                    "llm_analysis_time": result.llm_analysis_time,
                    "total_time": result.total_time
                },
                "business_flow_results": self._build_business_flow_results(result),
                "llm_analysis_results": {
                    "similarity_analysis": result.llm_analysis.get("similarity_analysis", {}),
                    "motivation_analysis": result.llm_analysis.get("motivation_analysis", {}),
                    "analysis_summary": result.llm_analysis.get("analysis_summary", {})
                },
                # 供数据库持久化的扁平 reddit 数据
                "reddit_data": {
                    "posts": reddit_posts_flat,      # 改为 posts
                    "comments": reddit_comments_flat, # 改为 comments
                    "user_histories": user_histories  # 添加用户历史
                },
                "error_info": {
                    "has_error": not result.success,
                    "error_message": result.error_message
                },
                # 向下游保存时传递归属用户ID
                "_owner_user_id": result.owner_user_id
            }
            
            # 使用DataService保存完整会话数据
            search_query_obj = SearchQuery(query=result.translated_query)


            search_result_obj = SearchResult(
                query=search_query_obj,
                results=result.google_results,
                total_results=len(result.google_results),
                search_time=result.google_search_time,
                success=result.success,
                error_message=result.error_message
            )

            # 将search_result_obj添加到complete_data中
            complete_data["search_result_obj"] = search_result_obj.to_dict()

            filepath = self.data_service.save_complete_session(
                session_id=result.session_id,
                data_to_save=complete_data
            )
            
            self.logger.info(f"完整搜索结果已保存: {filepath}")
            
        except Exception as e:
            self.logger.error(f"保存搜索结果失败: {e}")
    
    def _build_business_flow_results(self, result: CogBridgesSearchResult) -> Dict[str, Any]:
        """构建业务流程结果"""
        return {
            "step1_grok_search": {
                "success": bool(result.google_results),
                "results": result.google_results if result.google_results else [],
                "time_taken": result.google_search_time
            },
            "step2_commenters_history": {
                "success": bool(result.commenters_history),
                "total_users_count": len(result.commenters_history) if result.commenters_history else 0,
                "users_analyzed": list(result.commenters_history.keys()) if result.commenters_history else [],
                "users_with_data": [
                    {
                        "username": username,
                        "subreddits": list(data.keys()) if isinstance(data, dict) else [],
                        "total_comments": sum(len(sub_data.get("comments", [])) for sub_data in data.values()) if isinstance(data, dict) else 0,
                        "total_posts": sum(len(sub_data.get("posts", [])) for sub_data in data.values()) if isinstance(data, dict) else 0
                    }
                    for username, data in (result.commenters_history.items() if result.commenters_history else [])
                    if isinstance(data, dict) and any(sub_data.get("comments") or sub_data.get("posts") for sub_data in data.values())
                ][:10],  # 限制显示前10个有数据的用户
                "time_taken": result.commenters_history_time
            },
            "step3_llm_analysis": {
                "success": bool(result.llm_analysis),
                "credibility_analysis_count": len(result.llm_analysis.get("credibility_analysis", {})) if result.llm_analysis else 0,
                "analysis_summary": result.llm_analysis.get("analysis_summary", {}),
                "time_taken": result.llm_analysis_time
            }
        }

    def get_statistics(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        stats = {
            "google_stats": {
                "api_configured": False,
                "request_count": 0,
                "total_search_time": 0,
                "average_search_time": 0,
                "search_method": "Google Custom Search API (disabled)"
            },
            "reddit_stats": self.reddit_service.get_statistics(),
            "business_config": {
                "max_search_results": self.max_search_results,
                "max_comments_per_post": self.max_comments_per_post,
                "max_user_comments": self.max_user_comments,
                "max_user_posts": self.max_user_posts
            }
        }

        # 添加LLM服务统计（如果可用）
        if self.llm_service.configured:
            stats["llm_stats"] = self.llm_service.get_stats()

        return stats

    async def _get_user_full_overview_history(self, username: str, max_items: int = 100) -> Dict[str, Any]:
        """获取单个用户的所有历史overview（帖子和评论）"""
        try:
            reddit = await self.reddit_service._ensure_async_reddit()
            redditor = await reddit.redditor(username)
            
            submissions = []
            comments = []
            total_count = 0
            start_time = time.time()
            
            # 获取所有历史overview（包括帖子和评论），按top排序
            async for item in redditor.top(limit=None):
                total_count += 1
                
                # 判断是帖子还是评论
                if hasattr(item, 'is_self') or hasattr(item, 'title'):  # 这是帖子
                    submission_info = await self._get_submission_info(item)
                    submissions.append(submission_info)
                else:  # 这是评论
                    comment_info = await self._get_comment_info(item)
                    comments.append(comment_info)
            
            processing_time = time.time() - start_time
            
            return {
                "status": "success",
                "user": username,
                "total_count": total_count,
                "submissions": submissions,
                "comments": comments,
                "processing_time": processing_time
            }
        except Exception as e:
            self.logger.error(f"获取用户 {username} 的完整overview历史失败: {e}")
            return {
                "status": "error",
                "user": username,
                "message": str(e)
            }

    async def _get_submission_info(self, submission) -> Dict[str, Any]:
        from utils.reddit_utils import serialize_submission
        return serialize_submission(submission)

    async def _get_comment_info(self, comment) -> Dict[str, Any]:
        from utils.reddit_utils import serialize_comment
        return serialize_comment(comment)

    # moved to utils.llm_utils: extract_high_score_content, process_credibility_results, generate_credibility_analysis_summary

    async def _step3_llm_analysis(self, result: CogBridgesSearchResult, cancel_event=None) -> Dict[str, Any]:
        """步骤3: 执行LLM分析功能"""
        llm_results = {
            "similarity_analysis": {},
            "motivation_analysis": {},
            "analysis_summary": {},
            "success": False,
            "error": None
        }
        
        try:
            # 检查LLM服务配置
            if not self.llm_service.configured:
                llm_results["error"] = "LLM服务未配置"
                return llm_results
            
            # 提取用户和subreddit信息
            if cancel_event is not None and cancel_event.is_set():
                llm_results["error"] = "cancelled"
                return llm_results
            users_data = self._extract_users_and_subreddits(result)
            self.logger.info(f"提取到 {len(users_data)} 个用户数据")
            
            if not users_data:
                llm_results["error"] = "未找到有效的用户数据"
                return llm_results
            
            # 执行评论者背景分析
            self.logger.info("执行评论者背景分析...")
            if cancel_event is not None and cancel_event.is_set():
                llm_results["error"] = "cancelled"
                return llm_results
            credibility_results = await self._analyze_commenter_credibility(users_data, result, cancel_event=cancel_event)
            llm_results["credibility_analysis"] = credibility_results

            # 生成分析总结
            from utils.llm_utils import generate_credibility_analysis_summary
            llm_results["analysis_summary"] = generate_credibility_analysis_summary(credibility_results)
            llm_results["success"] = True
            
            return llm_results
            
        except Exception as e:
            self.logger.error(f"LLM分析失败: {e}")
            llm_results["error"] = str(e)
            return llm_results
    
    def _extract_users_and_subreddits(self, search_result: CogBridgesSearchResult) -> List[Dict[str, Any]]:
        from utils.reddit_utils import extract_users_and_subreddits
        return extract_users_and_subreddits(search_result.commenters_history)
    
    async def _analyze_commenter_credibility(self, users_data: List[Dict[str, Any]], search_result: CogBridgesSearchResult, cancel_event=None) -> Dict[str, Any]:
        """分析评论者背景"""
        credibility_results = {}

        # 构建评论者背景分析（限额+并发控制）
        from utils.reddit_utils import build_focus_comments_by_user
        focus_comments_by_user = build_focus_comments_by_user(search_result.reddit_posts)

        # 优先覆盖展示评论作者，再补充高质量样本
        display_user_order: List[str] = list(focus_comments_by_user.keys())
        display_user_set = set(display_user_order)

        # 构建用户名->数据映射
        user_to_data: Dict[str, Dict[str, Any]] = {u["username"]: u["user_data"] for u in users_data}
        # 高分候选按信息量排序
        scored_candidates = []
        for username, user_data in user_to_data.items():
            from utils.llm_utils import extract_high_score_content
            high_score_content = extract_high_score_content(user_data)
            if high_score_content:
                scored_candidates.append((username, len(high_score_content)))
        scored_candidates.sort(key=lambda x: x[1], reverse=True)

        max_users_for_llm = max(1, int(getattr(config, 'LLM_MAX_USERS', 10)))
        selected_usernames: List[str] = []
        for u in display_user_order:
            if u in user_to_data and len(selected_usernames) < max_users_for_llm:
                selected_usernames.append(u)
        if len(selected_usernames) < max_users_for_llm:
            for u, _ in scored_candidates:
                if u not in selected_usernames and len(selected_usernames) < max_users_for_llm:
                    selected_usernames.append(u)

        if selected_usernames:
            # 切换为：单用户并发分析（不使用批量API）。
            llm_concurrency = max(1, int(getattr(config, 'LLM_CONCURRENCY', 999999)))
            sem = asyncio.Semaphore(min(llm_concurrency, max(1, len(selected_usernames))))
            self.logger.info(
                f"背景分析：使用单用户并发路径，用户数={len(selected_usernames)}，最大并发={getattr(config, 'LLM_CONCURRENCY', 999999)}"
            )

            async def worker(username: str) -> dict:
                async with sem:
                    start_ts = time.time()
                    self.logger.info(f"并发任务开始: 用户 {username}")
                    try:
                        if cancel_event is not None and cancel_event.is_set():
                            raise Exception('cancelled')
                        user_data = user_to_data.get(username, {})
                        from utils.llm_utils import extract_high_score_content
                        hsc = extract_high_score_content(user_data)
                        result = await self._analyze_single_commenter_credibility(
                            username,
                            hsc,
                            focus_comments=focus_comments_by_user.get(username, []),
                            query=search_result.query,
                            cancel_event=cancel_event,
                        )
                        dt = time.time() - start_ts
                        self.logger.info(f"并发任务完成: 用户 {username}，用时 {dt:.2f}s")
                        return result
                    except Exception as e:
                        dt = time.time() - start_ts
                        self.logger.error(f"并发任务失败: 用户 {username}，用时 {dt:.2f}s，错误: {e}")
                        return {"username": username, "error": str(e)}

            tasks = [worker(u) for u in selected_usernames]
            # 谁先完成先处理，进一步降低总体等待
            results_incremental = []
            for coro in asyncio.as_completed(tasks):
                try:
                    if cancel_event is not None and cancel_event.is_set():
                        raise Exception('cancelled')
                    res = await coro
                except Exception as e:
                    self.logger.error(f"并发任务异常: {e}")
                    res = e
                results_incremental.append(res)
                try:
                    # 增量更新日志（便于观察返回顺序）
                    uname = (res or {}).get("username") if isinstance(res, dict) else None
                    self.logger.info(f"增量结果已到达: {uname or 'unknown'}")
                except Exception:
                    pass
            from utils.llm_utils import process_credibility_results
            credibility_results = process_credibility_results(results_incremental)

        return credibility_results
    
    # moved to utils.llm_utils: extract_high_score_content, process_credibility_results, generate_credibility_analysis_summary
    
    async def _analyze_single_commenter_credibility(self, username: str, high_score_content: List[Dict[str, Any]], focus_comments: List[Dict[str, Any]] = None, query: str = "", cancel_event=None) -> Dict[str, Any]:
        """分析单个评论者的背景"""
        try:
            # 提取活跃社区信息
            active_subreddits = list(set(content.get("subreddit", "") for content in high_score_content if content.get("subreddit")))

            # 调用LLM分析评论者画像
            result = await self.llm_service.analyze_commenter_credibility(
                username=username,
                high_score_content=high_score_content,
                focus_comments=focus_comments or [],
                query=query,
                cancel_event=cancel_event,
            )

            # 添加活跃社区信息
            if "subreddits_active_in" not in result or not result["subreddits_active_in"]:
                result["subreddits_active_in"] = active_subreddits[:10]  # 限制最多10个

            return {
                "username": username,
                **result
            }

        except Exception as e:
            self.logger.error(f"用户{username}的背景分析失败: {e}")
            return {
                "username": username,
                "error": str(e)
            }

    # moved to utils.reddit_utils.build_focus_comments_by_user
    
    # moved to utils.llm_utils: extract_high_score_content, process_credibility_results, generate_credibility_analysis_summary

    # ------------------------------
    # 新增：用户账户简单代理
    # ------------------------------
    def get_user_by_email(self, email: str):
        """通过邮箱获取用户"""
        if self.data_service and self.data_service.storage_service and self.data_service.storage_service.is_available():
            return self.data_service.storage_service.get_user_by_email(email)

    def create_user(self, email: str, password_hash: str):
        """创建用户"""
        if self.data_service and self.data_service.storage_service and self.data_service.storage_service.is_available():
            return self.data_service.storage_service.create_user(email, password_hash)

    def update_user_last_login(self, user_id: int):
        """更新用户最后登录时间"""
        if self.data_service and self.data_service.storage_service and self.data_service.storage_service.is_available():
            return self.data_service.storage_service.update_user_last_login(user_id)

    def mark_user_email_verified(self, email: str):
        """标记用户邮箱已验证"""
        if self.data_service and self.data_service.storage_service and self.data_service.storage_service.is_available():
            return self.data_service.storage_service.mark_user_email_verified(email)