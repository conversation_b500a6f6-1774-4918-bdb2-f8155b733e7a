import os
import time
import json
import pytest
import asyncio
from unittest.mock import patch, AsyncMock, MagicMock

# Configure test environment with PostgreSQL (requires actual DB connection)
os.environ.setdefault("ENABLE_DATABASE", "True")
# Note: Tests now require actual PostgreSQL connection via DATABASE_URL
os.environ.setdefault("TEST_MODE", "True")

from api.app import create_app
from api.tokens import issue_token
from config import config as _cfg
from pathlib import Path


def _poll_until_done(client, session_id: str, timeout: float = 6.0):
    start = time.time()
    status = None
    data = None
    while time.time() - start < timeout:
        r = client.get(f"/api/search/progress/{session_id}")
        if r.status_code == 200:
            payload = r.get_json()
            status = payload.get("status")
            data = payload
            if status in ("completed", "error", "cancelled"):
                return status, data
        time.sleep(0.1)
    return status, data


@pytest.fixture()
def app_client(monkeypatch):
    # Stub external orchestrated async steps to avoid network
    from services.cogbridges_service import CogBridgesService

    async def fake_step1(self, query: str, cancel_event=None):
        return {
            "posts": [
                {
                    "success": True,
                    "post": {
                        "id": "p1",
                        "title": "",
                        "selftext": "",
                        "url": "https://reddit.com/r/a/comments/abc/title",
                        "permalink": "/r/a/comments/abc/title",
                        "author": "",
                        "subreddit": "a",
                        "score": 0,
                        "num_comments": 0,
                        "created_utc": None,
                    },
                    "comments": [
                        {"id": "c1", "body": "hello1", "author": "u1", "score": 1, "created_utc": None, "permalink": "https://reddit.com/r/a/comments/abc/comment/c1"},
                        {"id": "c2", "body": "hello2", "author": "u2", "score": 2, "created_utc": None, "permalink": "https://reddit.com/r/a/comments/abc/comment/c2"},
                        {"id": "c3", "body": "hello3", "author": "u3", "score": 3, "created_utc": None, "permalink": "https://reddit.com/r/a/comments/abc/comment/c3"},
                        {"id": "c4", "body": "hello4", "author": "u4", "score": 4, "created_utc": None, "permalink": "https://reddit.com/r/a/comments/abc/comment/c4"},
                        {"id": "c5", "body": "hello5", "author": "u5", "score": 5, "created_utc": None, "permalink": "https://reddit.com/r/a/comments/abc/comment/c5"}
                    ],
                    "commenters": ["u1", "u2", "u3", "u4", "u5"],
                }
            ],
            "google_results": [],
            "search_time": 0.01,
            "citations": [],
        }

    async def fake_step2(self, posts_data, cancel_event=None):
        return {
            "history": {"u1": {"_metadata": {"subreddits": ["a"]}}},
            "processing_time": 0.02,
            "selected_usernames": ["u1"],
        }

    async def fake_step3(self, result, cancel_event=None):
        return {"success": True, "analysis_summary": {}}

    monkeypatch.setattr(CogBridgesService, "_step1_grok_reddit_search", fake_step1, raising=True)
    monkeypatch.setattr(CogBridgesService, "_step2_get_commenters_history", fake_step2, raising=True)
    monkeypatch.setattr(CogBridgesService, "_step3_llm_analysis", fake_step3, raising=True)

    app = create_app()
    app.config.update(TESTING=True)
    with app.test_client() as c:
        yield c


def test_registration_verify_search_persist_ledger_and_history(app_client):
    # 1) Register
    email = f"user_{int(time.time())}@example.com"
    password = "S@fePassw0rd!"
    r = app_client.post("/api/auth/register", json={"email": email, "password": password})
    assert r.status_code == 200
    payload = r.get_json()
    assert payload.get("success") is True
    # Read verification code from local JSON file
    codes_file = Path(_cfg.DATA_DIR) / "auth_email_codes.json"
    # Allow a brief moment for file flush on slower FS
    for _ in range(20):
        if codes_file.exists():
            break
        time.sleep(0.05)
    assert codes_file.exists()
    codes = json.loads(codes_file.read_text(encoding="utf-8"))
    entry = (codes.get("codes") or {}).get(email.lower())
    assert entry and entry.get("code")
    dev_code = str(entry.get("code"))

    # 2) Verify to activate account and receive token
    v = app_client.post("/api/auth/verify", json={"email": email, "code": dev_code})
    assert v.status_code == 200
    verified = v.get_json()
    token = verified.get("token")
    assert token

    # 3) Start search attributed to this user
    s = app_client.post("/api/search", json={"query": "acceptance test"}, headers={"Authorization": f"Bearer {token}"})
    assert s.status_code == 200
    sid = s.get_json()["session_id"]

    status, progress_payload = _poll_until_done(app_client, sid, timeout=6.0)
    assert status in ("completed", "error")

    # 4) History should reflect persisted DB session
    h = app_client.get("/api/history", headers={"Authorization": f"Bearer {token}"})
    assert h.status_code == 200
    hist = h.get_json()
    assert hist.get("success") is True
    items = hist.get("data", [])
    # If any history items exist, ensure DB-sourced item includes counts
    if items:
        for it in items:
            if it.get("source") == "database":
                assert "reddit_posts_count" in it
                break

    # 5) Verify DB persistence and points ledger by querying DB directly
    from api.app import get_cogbridges_service
    service = get_cogbridges_service()
    assert service and service.data_service and service.data_service.storage_service
    from models.database_models import (
        SearchSession,
        RedditPost,
        RedditComment,
        UserAccount,
        PointsLedger,
        UserHistory,
    )
    with service.data_service.storage_service.get_session() as dbs:
        sess = dbs.get(SearchSession, sid)
        assert sess is not None
        assert sess.owner_user_id is not None
        # Posts/comments persisted
        posts = dbs.query(RedditPost).filter(RedditPost.session_id == sid).all()
        comments = dbs.query(RedditComment).filter(RedditComment.session_id == sid).all()
        assert len(posts) >= 0  # at least created table and saved minimal
        # User history saved for u1
        uhs = dbs.query(UserHistory).filter(UserHistory.session_id == sid).all()
        assert isinstance(uhs, list)

        # Points ledger contains registration grant and a search deduction record
        user = dbs.get(UserAccount, sess.owner_user_id)
        assert user is not None
        ledgers = dbs.query(PointsLedger).filter(PointsLedger.user_id == user.id).all()
        # Ensure at least one 'trial' grant and one 'search' deduction exist
        reasons = [l.reason for l in ledgers]
        assert any(r == 'trial' for r in reasons)
        assert any(r == 'search' for r in reasons)
