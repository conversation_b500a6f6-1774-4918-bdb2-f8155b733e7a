import { test, expect } from '@playwright/test'

const mockSearchStart = {
  success: true,
  session_id: 'search_123',
  message: 'Search started. Please poll for progress',
  poll_url: '/api/search/progress/search_123',
}

const mockProgressRunning = {
  success: true,
  session_id: 'search_123',
  status: 'running',
  progress: 80,
}

const mockProgressDone = {
  success: true,
  session_id: 'search_123',
  status: 'completed',
  progress: 100,
  result: {
    success: true,
    query: 'coffee beans',
    translated_query: 'coffee beans',
    session_id: 'search_123',
    timestamp: new Date().toISOString(),
    total_time: 1.2,
    google_results: [],
    reddit_posts: [
      {
        success: true,
        post: {
          id: 'p1',
          title: 'Great beans',
          selftext: '',
          url: 'https://reddit.com/r/coffee/comments/abc',
          permalink: '/r/coffee/comments/abc',
          author: 'poster',
          subreddit: 'r/coffee',
          score: 10,
          num_comments: 1,
          created_utc: 0,
        },
        comments: [
          {
            id: 'c1',
            body: 'I love Ethiopian coffee',
            author: 'user1',
            score: 5,
            created_utc: 0,
            permalink: 'https://reddit.com/r/coffee/comments/abc/comment/c1',
          },
        ],
        commenters: ['user1'],
      },
    ],
    commenters_history: {
      user1: { _metadata: { subreddits: ['r/coffee'] } },
    },
    llm_analysis: { credibility_analysis: {} },
  },
}

const mockHealth = { status: 'healthy', service: 'CogBridges API', timestamp: Date.now() / 1000 }

// happy path: input → loading → results
// 使用 network interception 避免真实网络

test('happy path: search smoke', async ({ page }) => {
  // intercept backend endpoints
  await page.route('**/api/health', route => route.fulfill({ json: mockHealth }))
  await page.route('**/api/auth/me', route => route.fulfill({ json: { authenticated: false } }))
  await page.route('**/api/search', route => route.fulfill({ json: mockSearchStart }))

  let progressHits = 0
  await page.route('**/api/search/progress/search_123', route => {
    progressHits++
    if (progressHits < 2) {
      return route.fulfill({ json: mockProgressRunning })
    }
    return route.fulfill({ json: mockProgressDone })
  })

  await page.goto('/')
  await expect(page.getByRole('img', { name: 'CogBridges' })).toBeVisible()

  const input = page.getByRole('textbox', { name: 'Search questions from Reddit' })
  await input.fill('coffee beans')
  await page.getByRole('button', { name: 'Submit search' }).click()

  // Loading page
  await expect(page.getByText('Working on your answers...')).toBeVisible()
  await expect(page.getByText('Progress')).toBeVisible()

  // Results page
  await expect(page.getByText('AI-curated answers')).toBeVisible({ timeout: 10000 })
  await expect(page.getByText('Ethiopian coffee')).toBeVisible()
})
