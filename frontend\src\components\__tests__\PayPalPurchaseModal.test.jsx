/* @vitest-environment jsdom */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import PayPalPurchaseModal from '../PayPalPurchaseModal';
import * as api from '../../services/api';

// Helper to render with explicit container for React 18/19
const renderWithContainer = (ui) => {
  const container = document.createElement('div');
  document.body.appendChild(container);
  return render(ui, { container });
};

// Mock the API module
vi.mock('../../services/api', () => ({
  createPayPalOrder: vi.fn(),
}));

// Mock environment variables
vi.mock('import.meta', () => ({
  env: {
    VITE_PAYPAL_CLIENT_ID: 'test-paypal-client-id'
  }
}));

describe('PayPalPurchaseModal', () => {
  const mockOnClose = vi.fn();
  const mockOnSuccess = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock PayPal SDK
    global.window.paypal = {
      Buttons: vi.fn(() => ({
        render: vi.fn()
      }))
    };
    
    // Mock document.createElement for script loading
    const mockScript = {
      src: '',
      async: false,
      onload: null,
      onerror: null
    };

    const originalCreateElement = document.createElement.bind(document);
    vi.spyOn(document, 'createElement').mockImplementation((tagName) => {
      if (tagName === 'script') {
        return mockScript;
      }
      return originalCreateElement(tagName);
    });
    
    const originalAppendChild = document.body.appendChild.bind(document.body);
    vi.spyOn(document.body, 'appendChild').mockImplementation((element) => {
      if (element === mockScript) {
        // Simulate script loading success
        setTimeout(() => {
          if (mockScript.onload) mockScript.onload();
        }, 0);
        // Do not append non-Node mockScript to DOM
        return element;
      }
      return originalAppendChild(element);
    });

    vi.spyOn(document.body, 'removeChild').mockImplementation((el) => {
      try {
        if (el && el.parentNode) {
          el.parentNode.removeChild(el);
        }
      } catch (_) {}
    });
    vi.spyOn(document.body, 'contains').mockReturnValue(true);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('renders modal when isOpen is true', async () => {
    const utils = renderWithContainer(
      <PayPalPurchaseModal
        isOpen={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
      />
    );

    await waitFor(() => {
      expect(document.body.textContent).toContain('Purchase Points');
    });
    expect(document.body.textContent).toContain('$4.50');
    expect(document.body.textContent).toContain('$7.50');
    expect(document.body.textContent).toContain('$15.00');
  });

  it('does not render modal when isOpen is false', () => {
    renderWithContainer(
      <PayPalPurchaseModal
        isOpen={false}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
      />
    );

    expect(screen.queryByText('Purchase Points')).not.toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', () => {
    renderWithContainer(
      <PayPalPurchaseModal
        isOpen={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
      />
    );

    const closeButton = document.querySelector('button');
    fireEvent.click(closeButton);

    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('displays price options correctly', () => {
    renderWithContainer(
      <PayPalPurchaseModal
        isOpen={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
      />
    );

    // Check all price options are displayed
    expect(document.body.textContent).toContain('250 points');
    expect(document.body.textContent).toContain('500 points');
    expect(document.body.textContent).toContain('1,000 points');
    
    // Check "Most Popular" badge
    expect(screen.getByText('Most Popular')).toBeInTheDocument();
  });

  it('shows PayPal buttons with correct text initially', () => {
    renderWithContainer(
      <PayPalPurchaseModal
        isOpen={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
      />
    );

    // Should show PayPal buttons with correct text initially
    const paypalButtons = screen.getAllByText('Pay with PayPal');
    expect(paypalButtons).toHaveLength(3); // One for each price option
  });

  it('handles PayPal payment creation successfully', async () => {
    api.createPayPalOrder.mockResolvedValue({
      success: true,
      approval_url: 'https://paypal.com/approve/test'
    });

    // Mock window.location.href
    delete window.location;
    window.location = { href: '' };

    renderWithContainer(
      <PayPalPurchaseModal
        isOpen={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
      />
    );

    // Wait for PayPal SDK to load
    await waitFor(() => {
      expect(global.window.paypal).toBeDefined();
    });

    // Wait for interactive PayPal buttons and click the first one
    const paypalButtons = await screen.findAllByText(/Pay with PayPal/);
    fireEvent.click(paypalButtons[0]);

    await waitFor(() => {
      expect(api.createPayPalOrder).toHaveBeenCalledWith('250');
    });

    await waitFor(() => {
      expect(window.location.href).toBe('https://paypal.com/approve/test');
    });
  });

  it('handles PayPal payment creation error', async () => {
    api.createPayPalOrder.mockRejectedValue(new Error('Payment creation failed'));

    renderWithContainer(
      <PayPalPurchaseModal
        isOpen={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
      />
    );

    // Wait for PayPal SDK to load and for the buttons to become interactive
    await waitFor(() => {
      expect(global.window.paypal).toBeDefined();
    });
    const paypalButtons = await screen.findAllByText(/Pay with PayPal/);

    // Simulate PayPal button click
    fireEvent.click(paypalButtons[0]);

    await waitFor(() => {
      expect(screen.getByText('Payment creation failed')).toBeInTheDocument();
    });
  });

  it('displays PayPal security message', () => {
    renderWithContainer(
      <PayPalPurchaseModal
        isOpen={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
      />
    );

    expect(screen.getByText(/Payment processed securely by PayPal/)).toBeInTheDocument();
  });

  it('loads PayPal SDK script when modal opens', () => {
    renderWithContainer(
      <PayPalPurchaseModal
        isOpen={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
      />
    );

    expect(document.createElement).toHaveBeenCalledWith('script');
    expect(document.body.appendChild).toHaveBeenCalled();
  });
});
