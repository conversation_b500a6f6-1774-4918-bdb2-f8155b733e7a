import time
from typing import Any, Dict, Optional
import os
import json
from pathlib import Path

search_status: Dict[str, Dict[str, Any]] = {}
active_search_tasks: Dict[str, Dict[str, Any]] = {}

# Cross-process, best-effort persistence (works across Gunicorn workers in the same container)
_PROGRESS_DIR = Path(os.environ.get("SEARCH_PROGRESS_DIR", "/tmp/cogbridges_progress"))
try:
    _PROGRESS_DIR.mkdir(parents=True, exist_ok=True)
except Exception:
    pass


def _progress_file_path(session_id: str) -> Path:
    safe_id = "".join(ch for ch in str(session_id) if ch.isalnum() or ch in ("_", "-"))
    return _PROGRESS_DIR / f"{safe_id}.json"


def _atomic_write_json(path: Path, data: Dict[str, Any]) -> None:
    try:
        tmp = path.with_suffix(".json.tmp")
        with tmp.open("w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False)
        tmp.replace(path)
    except Exception:
        # Best-effort only; ignore persistence errors to avoid breaking the request path
        pass


def get_search_status(session_id: str) -> Optional[Dict[str, Any]]:
    """Get search status from memory or file.

    Returns None if not found.
    """
    info = search_status.get(session_id)
    if info is not None:
        return info
    # Fallback to file
    try:
        p = _progress_file_path(session_id)
        if p.exists():
            with p.open("r", encoding="utf-8") as f:
                data = json.load(f)
                # Cache into memory for faster next reads
                if isinstance(data, dict):
                    search_status[session_id] = data
                    return data
    except Exception:
        pass
    return None


def update_search_status(session_id: str, status: str, progress: int = 0, error: str | None = None, result: dict | None = None, start_time: float | None = None):
    payload: Dict[str, Any] = {
        'status': status,
        'progress': progress,
        'error': error,
        'result': result,
        'timestamp': time.time(),
    }
    
    # If this is the first update (running status) and start_time is provided, store it
    if status == 'running' and start_time is not None:
        payload['start_time'] = start_time
    # Otherwise, preserve existing start_time if it exists
    elif session_id in search_status and 'start_time' in search_status[session_id]:
        payload['start_time'] = search_status[session_id]['start_time']
    
    search_status[session_id] = payload
    # Persist to file so other workers can read it
    _atomic_write_json(_progress_file_path(session_id), payload)


def clear_search_status(session_id: str) -> None:
    """Optional helper to remove status from memory and file."""
    try:
        search_status.pop(session_id, None)
    except Exception:
        pass
    try:
        p = _progress_file_path(session_id)
        if p.exists():
            p.unlink(missing_ok=True)
    except Exception:
        pass