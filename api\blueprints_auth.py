from flask import Blueprint, jsonify, request
from utils.logger_utils import get_logger
from config import config as _cfg
from .app import get_cogbridges_service
from services.auth_service import AuthService
from services.email_service import EmailService
from models.database_models import UserAccount
from .tokens import issue_token as _issue_token, verify_token as _verify_token
import hashlib
import requests
import re
from flask import current_app
try:
    from google.auth.transport import requests as google_requests
    from google.oauth2 import id_token
    _GOOGLE_AUTH_AVAILABLE = True
except Exception:
    google_requests = None
    id_token = None
    _GOOGLE_AUTH_AVAILABLE = False
import json

logger = get_logger(__name__)

bp_auth = Blueprint("auth", __name__)
_auth_fallback = AuthService()
_email_service = EmailService()


# Password policy and leak detection (enabled in production)
def _should_enforce_password_policy() -> bool:
    try:
        if current_app and current_app.config.get('TESTING'):
            return False
    except Exception:
        pass
    try:
        if getattr(_cfg, 'DEBUG_MODE', False) or getattr(_cfg, 'TEST_MODE', False):
            return False
    except Exception:
        pass
    return True


def _is_password_strong(password: str, email: str) -> (bool, str):
    pwd = password or ""
    if len(pwd) < 8:
        return False, "Password too weak: at least 8 characters"
    categories = 0
    categories += 1 if re.search(r"[a-z]", pwd) else 0
    categories += 1 if re.search(r"[A-Z]", pwd) else 0
    categories += 1 if re.search(r"[0-9]", pwd) else 0
    categories += 1 if re.search(r"[^A-Za-z0-9]", pwd) else 0
    if categories < 2:
        return False, "Password too weak: include at least two of [lowercase, uppercase, digit, symbol]"
    # Simple weak password blacklist and email correlation
    common_weak = [
        "password", "123456", "qwerty", "111111", "abc123", "letmein", "welcome", "admin",
        "p@ssw0rd", "passw0rd", "12345678", "iloveyou"
    ]
    lowered = pwd.lower()
    if any(w in lowered for w in common_weak):
        return False, "Password too weak: too common"
    local_part = (email or "").split("@")[0].lower()
    if local_part and local_part in lowered:
        return False, "Password too weak: should not contain your email name"
    return True, ""


def _is_password_pwned(password: str) -> bool:
    try:
        sha1 = hashlib.sha1(password.encode('utf-8')).hexdigest().upper()
        prefix, suffix = sha1[:5], sha1[5:]
        url = f"https://api.pwnedpasswords.com/range/{prefix}"
        # Use short timeout to avoid registration being blocked for too long
        resp = requests.get(url, timeout=3)
        if resp.status_code != 200:
            return False
        for line in resp.text.splitlines():
            try:
                sfx, count = line.strip().split(":")
                if sfx.upper() == suffix:
                    return int(count) > 0
            except Exception:
                continue
        return False
    except Exception:
        # Do not block registration on network failure
        return False


@bp_auth.post('/api/auth/register')
def auth_register():
    try:
        data = request.get_json() or {}
        email = (data.get('email') or '').strip().lower()
        password = data.get('password') or ''
        code = (data.get('code') or '').strip()
        if not email or not password:
            return jsonify({"success": False, "error": "Email and password are required"}), 400

        # Only strictly validate in production environment to avoid breaking test/development flows
        if _should_enforce_password_policy() and not code:
            ok, msg = _is_password_strong(password, email)
            if not ok:
                return jsonify({"success": False, "error": msg}), 400
            if _is_password_pwned(password):
                return jsonify({"success": False, "error": "Password appears in public breach datasets. Please choose a different password"}), 400

        service = get_cogbridges_service()
        # code path: finalize registration
        if code:
            existing_user = None
            try:
                if service and service.data_service and service.data_service.storage_service and service.data_service.storage_service.is_available():
                    existing_user = service.get_user_by_email(email)
            except Exception:
                existing_user = None
            if not existing_user:
                existing_user = _auth_fallback.get_user_by_email(email)
            if not existing_user:
                return jsonify({"success": False, "error": "User not found or registration not initiated"}), 404
            if not _auth_fallback.verify_code(email, code):
                return jsonify({"success": False, "error": "Invalid or expired verification code"}), 400
            try:
                if service and service.data_service and service.data_service.storage_service and service.data_service.storage_service.is_available():
                    service.mark_user_email_verified(email)
                else:
                    _auth_fallback.mark_email_verified(email)
            except Exception:
                _auth_fallback.mark_email_verified(email)

            # Idempotent post-verify grants: ensure initial registration bonus was granted
            try:
                if service and service.data_service and service.data_service.storage_service and service.data_service.storage_service.is_available():
                    from models.database_models import UserAccount, PointsLedger
                    with service.data_service.storage_service.get_session() as db_session:
                        user_obj2 = service.get_user_by_email(email)
                        user2_id = None
                        try:
                            user2_id = getattr(user_obj2, 'id', user_obj2.get('id') if isinstance(user_obj2, dict) else None)
                        except Exception:
                            user2_id = None
                        if user2_id:
                            u = db_session.get(UserAccount, user2_id)
                            if u:
                                flags = dict(u.trial_flags or {})
                                # If user somehow didn't get their registration bonus, grant it now
                                if not flags.get('granted20') or not flags.get('granted40'):
                                    total_grant = int(_cfg.TRIAL_INITIAL_POINTS) + int(_cfg.TRIAL_VERIFIED_POINTS)
                                    u.points_balance = int(u.points_balance or 0) + total_grant
                                    flags['granted20'] = True
                                    flags['granted40'] = True
                                    entry = PointsLedger(
                                        user_id=u.id,
                                        delta=total_grant,
                                        reason='trial',
                                        ref='registration_bonus',
                                        meta={'type': 'email_verified', 'searches_granted': 2}
                                    )
                                    db_session.add(entry)
                                    u.trial_flags = flags
                                    db_session.commit()
                                    logger.info(f"User {u.id} granted missing registration bonus on email verification")
            except Exception as _e:
                logger.error(f"post-verify grant check failed: {_e}")

            uid_raw = existing_user.get('id') if isinstance(existing_user, dict) else getattr(existing_user, 'id', None)
            uid = int(uid_raw) if uid_raw is not None else None
            token = _issue_token(uid)
            resp = jsonify({"success": True, "token": token, "user": {"id": uid, "email": email, "email_verified": True}})
            resp.set_cookie('auth_token', token, httponly=True, samesite='Lax')
            return resp

        # create or resend code
        existing_user = None
        db_available = bool(service and service.data_service and service.data_service.storage_service and service.data_service.storage_service.is_available())
        if db_available:
            try:
                existing_user = service.get_user_by_email(email)
            except Exception:
                existing_user = None
        if not existing_user:
            existing_user = _auth_fallback.get_user_by_email(email)
        if existing_user:
            email_verified = bool(getattr(existing_user, 'email_verified', False) if not isinstance(existing_user, dict) else existing_user.get('email_verified', False))
            if email_verified:
                return jsonify({"success": False, "error": "Email already exists"}), 409
            from random import randint
            verify_code = f"{randint(100000, 999999)}"
            _auth_fallback.generate_and_store_code(email, verify_code)
            ok = _email_service.send_verification_code(email, verify_code)
            payload = {"success": True, "need_verification": True, "message": "Verification code sent to your email"}
            if (not ok) and _cfg.DEBUG_MODE:
                payload["dev_code"] = verify_code
            try:
                if (not _email_service.configured) and _cfg.DEBUG_MODE:
                    payload["dev_code"] = verify_code
            except Exception:
                pass
            return jsonify(payload)
        # create user
        user_obj = None
        if db_available:
            try:
                from werkzeug.security import generate_password_hash
                user_obj = service.create_user(email, generate_password_hash(password))
            except Exception as _e:
                logger.error(f"Primary storage user creation failed: {_e}")
                user_obj = None
        else:
            user_obj = _auth_fallback.create_user(email, password)
        if not user_obj:
            return jsonify({"success": False, "error": "Registration failed"}), 500
        
        # Grant initial points (40 total = 20 initial + 20 bonus) and record ledger
        try:
            logger.info(f"Attempting to grant initial points to user_obj: {user_obj}")
            if db_available and user_obj:
                from models.database_models import PointsLedger, UserAccount
                with service.data_service.storage_service.get_session() as db_session:
                    user_id = getattr(user_obj, 'id', user_obj.get('id') if isinstance(user_obj, dict) else None)
                    logger.info(f"User ID extracted: {user_id}")
                    user = db_session.get(UserAccount, user_id)
                    if user:
                        logger.info(f"User found in DB, current balance: {user.points_balance}")
                        # Grant 40 points total (2 searches)
                        total_grant = int(_cfg.TRIAL_INITIAL_POINTS) + int(_cfg.TRIAL_VERIFIED_POINTS)
                        user.points_balance = int(user.points_balance or 0) + total_grant
                        trial_flags = dict(user.trial_flags or {})
                        trial_flags.update({"granted20": True, "granted40": True})
                        user.trial_flags = trial_flags
                        
                        # Record as a single registration bonus
                        ledger_entry = PointsLedger(
                            user_id=user_id,
                            delta=total_grant,
                            reason='trial',
                            ref='registration_bonus',
                            meta={'type': 'registration', 'searches_granted': 2}
                        )
                        db_session.add(ledger_entry)
                        db_session.commit()
                        logger.info(f"User {user_id} registration granted {total_grant} points (2 searches), new balance: {user.points_balance}")
                    else:
                        logger.error(f"User with ID {user_id} not found in session")
        except Exception as e:
            logger.error(f"Failed to grant initial points: {str(e)}", exc_info=True)
        
        try:
            from random import randint
            verify_code = f"{randint(100000, 999999)}"
            _auth_fallback.generate_and_store_code(email, verify_code)
            ok = _email_service.send_verification_code(email, verify_code)
            payload = {"success": True, "need_verification": True, "message": "Verification code sent to your email"}
            if (not ok) and _cfg.DEBUG_MODE:
                payload["dev_code"] = verify_code
            try:
                if (not _email_service.configured) and _cfg.DEBUG_MODE:
                    payload["dev_code"] = verify_code
            except Exception:
                pass
            return jsonify(payload)
        except Exception as e:
            return jsonify({"success": False, "error": f"Failed to send verification code: {str(e)}"}), 500
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@bp_auth.post('/api/auth/login')
def auth_login():
    try:
        data = request.get_json() or {}
        email = (data.get('email') or '').strip().lower()
        password = data.get('password') or ''
        if not email or not password:
            return jsonify({"success": False, "error": "Email and password are required"}), 400
        service = get_cogbridges_service()
        user_obj = None
        password_hash = None
        try:
            if service and service.data_service and service.data_service.storage_service and service.data_service.storage_service.is_available():
                user_obj = service.get_user_by_email(email)
                if user_obj:
                    password_hash = getattr(user_obj, 'password_hash', None)
        except Exception:
            user_obj = None
        if not user_obj:
            user_obj = _auth_fallback.get_user_by_email(email)
            if user_obj:
                password_hash = user_obj.get('password_hash')
        if not user_obj or not password_hash:
            return jsonify({"success": False, "error": "Invalid email or password"}), 401
        from werkzeug.security import check_password_hash
        if not check_password_hash(password_hash, password):
            return jsonify({"success": False, "error": "Invalid email or password"}), 401
        email_verified = bool(getattr(user_obj, 'email_verified', False) if not isinstance(user_obj, dict) else user_obj.get('email_verified', False))
        is_active = bool(getattr(user_obj, 'is_active', True) if not isinstance(user_obj, dict) else user_obj.get('is_active', True))
        if not email_verified or not is_active:
            return jsonify({"success": False, "error": "Account not verified. Please verify your email first", "need_verification": True}), 403
        try:
            uid_raw = user_obj.get('id') if isinstance(user_obj, dict) else getattr(user_obj, 'id', None)
            uid = int(uid_raw) if uid_raw is not None else None
            if service and hasattr(service, 'update_user_last_login'):
                service.update_user_last_login(uid)
            else:
                _auth_fallback.update_user_last_login(uid)
        except Exception:
            pass
        token = _issue_token(uid)
        resp = jsonify({"success": True, "user": {"id": uid, "email": email}, "token": token})
        resp.set_cookie('auth_token', token, httponly=True, samesite='Lax')
        return resp
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@bp_auth.post('/api/auth/send-code')
def auth_send_code():
    try:
        data = request.get_json() or {}
        email = (data.get('email') or '').strip().lower()
        if not email:
            return jsonify({"success": False, "error": "Email is required"}), 400
        service = get_cogbridges_service()
        exists = False
        try:
            if service and service.data_service and service.data_service.storage_service and service.data_service.storage_service.is_available():
                exists = bool(service.get_user_by_email(email))
        except Exception:
            exists = False
        if not exists and not _auth_fallback.get_user_by_email(email):
            return jsonify({"success": False, "error": "User not found"}), 404
        from random import randint
        verify_code = f"{randint(100000, 999999)}"
        _auth_fallback.generate_and_store_code(email, verify_code)
        ok = _email_service.send_verification_code(email, verify_code)
        payload = {"success": True, "message": "Verification code sent"}
        if (not ok) and _cfg.DEBUG_MODE:
            payload["dev_code"] = verify_code
            payload["message"] = "SMTP unavailable, returning dev verification code"
        return jsonify(payload)
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@bp_auth.post('/api/auth/verify')
def auth_verify_code():
    try:
        data = request.get_json() or {}
        email = (data.get('email') or '').strip().lower()
        code = (data.get('code') or '').strip()
        if not email or not code:
            return jsonify({"success": False, "error": "Email and verification code are required"}), 400
        service = get_cogbridges_service()
        ok = _auth_fallback.verify_code(email, code)
        if not ok:
            return jsonify({"success": False, "error": "Invalid or expired verification code"}), 400
        try:
            db_available = bool(service and service.data_service and service.data_service.storage_service and service.data_service.storage_service.is_available())
            if db_available:
                # 标记邮箱已验证
                service.mark_user_email_verified(email)
                # Check if user already got their registration bonus
                try:
                    from models.database_models import UserAccount, PointsLedger
                    with service.data_service.storage_service.get_session() as db_session:
                        user = service.get_user_by_email(email)
                        if user:
                            u = db_session.get(UserAccount, user.id)
                            if u:
                                flags = dict(u.trial_flags or {})
                                # Only grant if not already granted
                                if not flags.get('granted20') or not flags.get('granted40'):
                                    total_grant = int(_cfg.TRIAL_INITIAL_POINTS) + int(_cfg.TRIAL_VERIFIED_POINTS)
                                    u.points_balance = int(u.points_balance or 0) + total_grant
                                    flags['granted20'] = True
                                    flags['granted40'] = True
                                    entry = PointsLedger(
                                        user_id=u.id,
                                        delta=total_grant,
                                        reason='trial',
                                        ref='registration_bonus',
                                        meta={'type': 'email_verified', 'searches_granted': 2}
                                    )
                                    db_session.add(entry)
                                    u.trial_flags = flags
                                    db_session.commit()
                                    logger.info(f"User {u.id} granted registration bonus on email verification")
                                else:
                                    logger.info(f"User {u.id} already has registration bonus, skipping")
                except Exception as _e:
                    logger.error(f"grant registration bonus failed: {_e}")
            else:
                _auth_fallback.mark_email_verified(email)
        except Exception:
            _auth_fallback.mark_email_verified(email)

        # Prioritize getting user from database (if exists), otherwise fallback to local JSON
        user_obj = None
        try:
            if service and service.data_service and service.data_service.storage_service and service.data_service.storage_service.is_available():
                user_obj = service.get_user_by_email(email)
        except Exception:
            user_obj = None
        if not user_obj:
            user_obj = _auth_fallback.get_user_by_email(email)

        uid_raw = (user_obj.get('id') if isinstance(user_obj, dict) else getattr(user_obj, 'id', None)) if user_obj else None
        uid = int(uid_raw) if uid_raw is not None else None
        if uid:
            token = _issue_token(uid)
            payload = jsonify({
                "success": True,
                "user": {"id": uid, "email": email, "email_verified": True},
                # Return token in JSON for frontend to put into Authorization header in cross-domain environment,
                # to ensure subsequent search requests can correctly attribute to the user and write to history.
                "token": token,
            })
            payload.set_cookie('auth_token', token, httponly=True, samesite='Lax')
            return payload
        return jsonify({"success": True, "email_verified": True})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@bp_auth.post('/api/auth/logout')
def auth_logout():
    resp = jsonify({"success": True})
    resp.delete_cookie('auth_token')
    return resp


@bp_auth.route('/api/auth/google', methods=['OPTIONS'])
def auth_google_options():
    """Handle preflight OPTIONS request for Google auth"""
    return '', 200


@bp_auth.post('/api/auth/google')
def auth_google():
    """Handle Google OAuth login/register"""
    if not _GOOGLE_AUTH_AVAILABLE:
        return jsonify({"success": False, "error": "Google auth dependencies not installed"}), 501
    try:
        data = request.get_json() or {}
        credential = data.get('credential')
        
        if not credential:
            return jsonify({"success": False, "error": "Google credential is required"}), 400
            
        # Verify the Google ID token
        try:
            # Specify the CLIENT_ID of the app that accesses the backend
            idinfo = id_token.verify_oauth2_token(
                credential, 
                google_requests.Request(), 
                _cfg.GOOGLE_CLIENT_ID
            )
            
            # ID token is valid
            email = idinfo.get('email', '').lower()
            email_verified = idinfo.get('email_verified', False)
            name = idinfo.get('name', '')
            picture = idinfo.get('picture', '')
            google_id = idinfo.get('sub')
            
            if not email or not email_verified:
                return jsonify({"success": False, "error": "Email not verified with Google"}), 400
                
        except ValueError as e:
            # Invalid token
            logger.error(f"Google OAuth token validation failed: {str(e)}")
            return jsonify({"success": False, "error": "Invalid Google credentials"}), 401
            
        # Check if user exists
        service = get_cogbridges_service()
        user_obj = None
        db_available = bool(service and service.data_service and service.data_service.storage_service and service.data_service.storage_service.is_available())
        
        if db_available:
            try:
                user_obj = service.get_user_by_email(email)
            except Exception:
                user_obj = None
                
        if not user_obj:
            user_obj = _auth_fallback.get_user_by_email(email)
            
        if user_obj:
            # User exists - log them in
            uid_raw = user_obj.get('id') if isinstance(user_obj, dict) else getattr(user_obj, 'id', None)
            uid = int(uid_raw) if uid_raw is not None else None
            
            # Update last login
            try:
                if service and hasattr(service, 'update_user_last_login'):
                    service.update_user_last_login(uid)
                else:
                    _auth_fallback.update_user_last_login(uid)
            except Exception:
                pass
                
            token = _issue_token(uid)
            resp = jsonify({
                "success": True, 
                "user": {"id": uid, "email": email},
                "token": token,
                "is_new_user": False
            })
            resp.set_cookie('auth_token', token, httponly=True, samesite='Lax')
            return resp
        else:
            # New user - create account with Google OAuth
            # Generate a random secure password since user won't use it
            import secrets
            random_password = secrets.token_urlsafe(32)
            
            try:
                if db_available:
                    from werkzeug.security import generate_password_hash
                    user_obj = service.create_user(email, generate_password_hash(random_password))
                    # Mark as email verified since Google verified it
                    service.mark_user_email_verified(email)
            except Exception as e:
                logger.error(f"Failed to create user in database: {str(e)}")
                user_obj = None
                
            if not user_obj:
                user_obj = _auth_fallback.create_user(email, random_password)
                if user_obj:
                    _auth_fallback.mark_email_verified(email)
                    
            if not user_obj:
                return jsonify({"success": False, "error": "Failed to create user account"}), 500
                
            uid_raw = user_obj.get('id') if isinstance(user_obj, dict) else getattr(user_obj, 'id', None)
            uid = int(uid_raw) if uid_raw is not None else None
            
            # Grant registration bonus (40 points = 2 searches) for Google-registered users
            try:
                if db_available and user_obj:
                    from models.database_models import PointsLedger, UserAccount
                    with service.data_service.storage_service.get_session() as db_session:
                        user = db_session.get(UserAccount, uid)
                        if user:
                            # Grant 40 points (2 searches)
                            total_points = _cfg.TRIAL_INITIAL_POINTS + _cfg.TRIAL_VERIFIED_POINTS
                            user.points_balance = int(user.points_balance or 0) + total_points
                            trial_flags = dict(user.trial_flags or {})
                            trial_flags.update({"granted20": True, "granted40": True})
                            user.trial_flags = trial_flags
                            
                            # Record as single registration bonus
                            ledger_entry = PointsLedger(
                                user_id=uid,
                                delta=total_points,
                                reason='trial',
                                ref='registration_bonus',
                                meta={'type': 'google_registration', 'searches_granted': 2}
                            )
                            db_session.add(ledger_entry)
                            
                            db_session.commit()
                            logger.info(f"Google user {uid} granted {total_points} points (2 searches) on registration")
            except Exception as e:
                logger.error(f"Failed to grant registration bonus to Google user: {str(e)}", exc_info=True)
            
            token = _issue_token(uid)
            resp = jsonify({
                "success": True,
                "user": {"id": uid, "email": email, "email_verified": True},
                "token": token,
                "is_new_user": True
            })
            resp.set_cookie('auth_token', token, httponly=True, samesite='Lax')
            return resp
            
    except Exception as e:
        logger.error(f"Google OAuth error: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500


@bp_auth.route('/api/auth/me', methods=['OPTIONS'])
def auth_me_options():
    """Handle preflight OPTIONS request for auth me"""
    return '', 200


@bp_auth.get('/api/auth/me')
def auth_me():
    try:
        token = request.cookies.get('auth_token') or (request.headers.get('Authorization') or '').replace('Bearer ', '')
        if not token:
            return jsonify({"authenticated": False}), 200
        uid = _verify_token(token)
        if not uid:
            return jsonify({"authenticated": False}), 200
        service = get_cogbridges_service()
        user_obj = None
        try:
            if service and service.data_service and service.data_service.storage_service and service.data_service.storage_service.is_available():
                from models.database_models import UserAccount
                with service.data_service.storage_service.get_session() as s:
                    user_obj = s.get(UserAccount, uid)
        except Exception:
            user_obj = None
        if not user_obj:
            user_obj = _auth_fallback.get_user_by_id(uid)
        if not user_obj:
            return jsonify({"authenticated": False}), 200
        email = getattr(user_obj, 'email', None) or (user_obj.get('email') if isinstance(user_obj, dict) else None)
        return jsonify({"authenticated": True, "user": {"id": uid, "email": email}})
    except Exception as e:
        return jsonify({"authenticated": False, "error": str(e)}), 200


@bp_auth.route('/api/user/points', methods=['GET'])
def get_user_points():
    """Get current user's points balance"""
    try:
        # Extract token from cookie or Authorization header
        token = request.cookies.get('auth_token') or (request.headers.get('Authorization') or '').replace('Bearer ', '')
        if not token:
            return jsonify({"success": False, "error": "Not authenticated"}), 401

        # Verify token
        uid = _verify_token(token)
        if not uid:
            return jsonify({"success": False, "error": "Invalid token"}), 401

        # Get service
        service = get_cogbridges_service()
        db_available = bool(service and getattr(service, 'data_service', None) and 
                            getattr(service.data_service, 'storage_service', None) and 
                            service.data_service.storage_service.is_available())

        if not db_available:
            # Database not available, return default balance
            return jsonify({"success": True, "balance": 0})

        # Get user from database
        from models.database_models import UserAccount
        with service.data_service.storage_service.get_session() as db_session:
            user = db_session.get(UserAccount, uid)
            if not user:
                return jsonify({"success": False, "error": "User not found"}), 404

            balance = int(user.points_balance or 0)
            return jsonify({"success": True, "balance": balance})
            
    except Exception as e:
        logger.error(f"Error getting user points: {str(e)}", exc_info=True)
        return jsonify({"success": False, "error": "Internal server error"}), 500


@bp_auth.route('/api/user/points/history', methods=['GET'])
def get_points_history():
    """Get user's points transaction history"""
    try:
        # Extract token from cookie or Authorization header
        token = request.cookies.get('auth_token') or (request.headers.get('Authorization') or '').replace('Bearer ', '')
        if not token:
            return jsonify({"success": False, "error": "Not authenticated"}), 401

        # Verify token
        uid = _verify_token(token)
        if not uid:
            return jsonify({"success": False, "error": "Invalid token"}), 401

        # Get service
        service = get_cogbridges_service()
        db_available = bool(service and getattr(service, 'data_service', None) and 
                            getattr(service.data_service, 'storage_service', None) and 
                            service.data_service.storage_service.is_available())

        if not db_available:
            # Database not available, return empty history
            return jsonify({"success": True, "history": []})

        # Get points history from database
        from models.database_models import PointsLedger
        with service.data_service.storage_service.get_session() as db_session:
            history = db_session.query(PointsLedger).filter_by(
                user_id=uid
            ).order_by(PointsLedger.timestamp.desc()).limit(50).all()

            history_data = []
            for entry in history:
                history_data.append({
                    "id": entry.id,
                    "delta": entry.delta,
                    "reason": entry.reason,
                    "ref": entry.ref,
                    "timestamp": entry.timestamp.isoformat() if entry.timestamp else None,
                    "meta": entry.meta or {}
                })

            return jsonify({"success": True, "history": history_data})
            
    except Exception as e:
        logger.error(f"Error getting points history: {str(e)}", exc_info=True)
        return jsonify({"success": False, "error": "Internal server error"}), 500