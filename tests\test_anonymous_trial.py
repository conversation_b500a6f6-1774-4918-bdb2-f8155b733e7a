#!/usr/bin/env python3
"""
测试：未登录用户试用限制
"""

import pytest
from unittest.mock import patch


class TestAnonymousTrial:
    """测试未登录用户的试用限制"""
    
    def test_anonymous_search_blocked(self, test_client):
        """测试：未登录用户不能搜索"""
        # 不带cookie的请求
        response = test_client.post('/api/search', json={
            'query': 'test search'
        })
        
        assert response.status_code == 403
        data = response.get_json()
        assert data['success'] is False
        assert 'Please register to search' in data['error']
        assert 'Sign up now and get 2 free deep searches!' in data['error']
        assert data.get('need_login') is True
    
    def test_anonymous_with_cookie_still_blocked(self, test_client):
        """测试：未登录用户即使有cookie也不能搜索"""
        # 设置已使用试用标记的cookie（应该不再有影响）
        test_client.set_cookie(key='anonymous_trial_used', value='true')
        
        response = test_client.post('/api/search', 
            json={'query': 'second search'}
        )
        
        assert response.status_code == 403
        data = response.get_json()
        assert data['success'] is False
        assert 'Please register to search' in data['error']
        assert data.get('need_login') is True
    
    def test_logged_in_user_not_affected(self, test_client, auth_headers, db_session):
        """测试：登录用户不受未登录限制影响"""
        # Create a user with points in the database
        from models.database_models import UserAccount
        user = UserAccount(
            email='<EMAIL>',
            password_hash='dummy_hash',
            points_balance=100,  # Enough points for searches
            email_verified=True,
            is_active=True
        )
        db_session.add(user)
        db_session.commit()
        
        # Mock the token verification to return this user's ID
        with patch('api.blueprints_search._verify_token', return_value=user.id):
            # Even with anonymous_trial_used cookie, logged in user should be able to search
            response = test_client.post('/api/search',
                json={'query': 'logged in search'},
                headers=auth_headers
            )
            
            # Should succeed (200) or get other errors, but not 403 (authentication)
            assert response.status_code != 403
            # It will likely be 200 and start the search
            if response.status_code == 200:
                data = response.get_json()
                assert data['success'] is True
    
    def test_no_cookie_set_for_anonymous(self, test_client):
        """测试：未登录用户尝试搜索时不会设置cookie"""
        # 第一次搜索
        response = test_client.post('/api/search', json={
            'query': 'first search'
        })
        
        assert response.status_code == 403
        
        # 不应该设置cookie
        cookie_header = response.headers.get('Set-Cookie')
        assert cookie_header is None or 'anonymous_trial_used' not in cookie_header
    
    def test_different_queries_same_result(self, test_client):
        """测试：同一用户不同查询都被阻止"""
        # 第一次搜索
        response1 = test_client.post('/api/search', json={
            'query': 'python tutorial'
        })
        assert response1.status_code == 403
        
        # 第二次搜索，不同查询
        response2 = test_client.post('/api/search',
            json={'query': 'javascript guide'}
        )
        
        assert response2.status_code == 403
        data = response2.get_json()
        assert 'Please register to search' in data['error']
    
    def test_registration_after_anonymous_attempt(self, test_client):
        """测试：尝试匿名搜索后仍可以注册"""
        # 先尝试匿名搜索
        response1 = test_client.post('/api/search', json={
            'query': 'anonymous search'
        })
        assert response1.status_code == 403
        
        # 然后注册
        response2 = test_client.post('/api/auth/register',
            json={
                'email': '<EMAIL>',
                'password': 'testpass123'
            }
        )
        
        # 注册应该成功
        assert response2.status_code in [200, 400]  # 200成功或400需要验证


if __name__ == '__main__':
    pytest.main([__file__, '-v'])