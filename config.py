"""
CogBridges Search - 配置管理模块
统一管理所有配置项，支持环境变量和默认值
"""

import os
from pathlib import Path
from typing import Optional, List
from dotenv import load_dotenv

# 兼容空值/非法值的环境变量读取助手
def _env_int(name: str, default_value: int) -> int:
    value = os.getenv(name)
    try:
        if value is None or str(value).strip() == "":
            return int(default_value)
        return int(value)
    except Exception:
        return int(default_value)

def _env_float(name: str, default_value: float) -> float:
    value = os.getenv(name)
    try:
        if value is None or str(value).strip() == "":
            return float(default_value)
        return float(value)
    except Exception:
        return float(default_value)

# 加载环境变量
load_dotenv()

class Config:
    """应用配置类"""
    
    # =============================================================================
    # 基础路径配置
    # =============================================================================
    BASE_DIR = Path(__file__).parent
    DATA_DIR = BASE_DIR / os.getenv("DATA_DIR", "data")
    LOGS_DIR = BASE_DIR / os.getenv("LOGS_DIR", "data/logs")
    RESULTS_DIR = BASE_DIR / os.getenv("RESULTS_DIR", "data/results")
    
    # 确保目录存在
    DATA_DIR.mkdir(exist_ok=True)
    LOGS_DIR.mkdir(exist_ok=True)
    RESULTS_DIR.mkdir(exist_ok=True)
    
    # =============================================================================
    # Flask 应用配置
    # =============================================================================
    SECRET_KEY = os.getenv("SECRET_KEY", "cogbridges-search-secret-key-2024")
    FLASK_ENV = os.getenv("FLASK_ENV", "development")
    FLASK_DEBUG = os.getenv("FLASK_DEBUG", "True").lower() == "true"
    # 将默认HOST设为0.0.0.0，便于在同一局域网的手机访问
    HOST = os.getenv("HOST", "0.0.0.0")
    PORT = int(os.getenv("PORT", "5000"))
    

    
    # =============================================================================
    # Reddit API 配置
    # =============================================================================
    REDDIT_CLIENT_ID = os.getenv("REDDIT_CLIENT_ID", "")
    REDDIT_CLIENT_SECRET = os.getenv("REDDIT_CLIENT_SECRET", "")
    REDDIT_USER_AGENT = os.getenv("REDDIT_USER_AGENT", "CogBridges-Search/1.0")
    REDDIT_TOP_COMMENTS_COUNT = int(os.getenv("REDDIT_TOP_COMMENTS_COUNT", "6"))
    REDDIT_API_RATE_LIMIT = int(os.getenv("REDDIT_API_RATE_LIMIT", "60"))  # 每分钟请求数
    
    # Reddit配置验证
    @property
    def reddit_configured(self) -> bool:
        """检查Reddit API是否已配置"""
        return bool(self.REDDIT_CLIENT_ID and self.REDDIT_CLIENT_SECRET)
    
    # =============================================================================
    # 用户历史数据配置
    # =============================================================================
    USER_HISTORY_COMMENTS_COUNT = int(os.getenv("USER_HISTORY_COMMENTS_COUNT", "100"))
    USER_HISTORY_POSTS_COUNT = int(os.getenv("USER_HISTORY_POSTS_COUNT", "50"))
    

    
    # =============================================================================
    # 日志配置
    # =============================================================================
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO").upper()
    
    # 环境检测：Render或生产环境下禁用文件日志，只保留控制台日志
    IS_PRODUCTION = os.getenv("RENDER", "false").lower() == "true" or os.getenv("FLASK_ENV", "development") == "production"
    
    # 文件日志：本地开发启用，生产环境禁用（避免无用的文件写入和存储占用）
    SAVE_DETAILED_LOGS = os.getenv("SAVE_DETAILED_LOGS", "False" if IS_PRODUCTION else "True").lower() == "true"
    
    # JSON格式日志：控制台输出是否使用JSON格式（便于生产环境日志解析）
    ENABLE_JSON_LOGS = os.getenv("ENABLE_JSON_LOGS", "False").lower() == "true"
    
    # I/O快照：本地开发启用，生产环境禁用（避免大量文件生成和存储消耗）
    ENABLE_IO_SNAPSHOTS = os.getenv("ENABLE_IO_SNAPSHOTS", "False" if IS_PRODUCTION else "True").lower() == "true"
    IO_SNAPSHOTS_MAX_CHARS = _env_int("IO_SNAPSHOTS_MAX_CHARS", 4000)
    
    # =============================================================================
    # 性能配置（按你的要求：默认不做限制，除非 .env 显式设置）
    # =============================================================================
    MAX_CONCURRENT_REQUESTS = _env_int("MAX_CONCURRENT_REQUESTS", 999999)
    REQUEST_TIMEOUT = _env_int("REQUEST_TIMEOUT", 30)
    MAX_RETRIES = _env_int("MAX_RETRIES", 0)
    REQUEST_DELAY = _env_float("REQUEST_DELAY", 0.0)

    # Reddit 抓取并发与限额（极宽松默认）
    REDDIT_POST_FETCH_CONCURRENCY = _env_int("REDDIT_POST_FETCH_CONCURRENCY", 999999)
    COMMENTERS_HISTORY_CONCURRENCY = _env_int("COMMENTERS_HISTORY_CONCURRENCY", 999999)
    COMMENTERS_HISTORY_MAX_USERS = _env_int("COMMENTERS_HISTORY_MAX_USERS", 999999)
    USER_OVERVIEW_MAX_ITEMS = _env_int("USER_OVERVIEW_MAX_ITEMS", 999999)

    # LLM 并发与限额（极宽松默认）
    LLM_CONCURRENCY = _env_int("LLM_CONCURRENCY", 999999)
    LLM_MAX_USERS = _env_int("LLM_MAX_USERS", 999999)
    
    # =============================================================================
    # Replicate API 配置
    # =============================================================================
    REPLICATE_API_TOKEN = os.getenv("REPLICATE_API_TOKEN", "")
    # 迁移至 GPT-5 系列：默认使用 gpt-5-mini 以平衡成本与速度；可置为 openai/gpt-5 以提升推理质量
    REPLICATE_MODEL = os.getenv("REPLICATE_MODEL", "openai/gpt-5-mini")
    # 统一 LLM 行为控制（批量分析已移除，仅保留占位不使用的键以兼容旧 .env）
    LLM_CREDIBILITY_MAX_TOKENS = _env_int("LLM_CREDIBILITY_MAX_TOKENS", 10000)

    # Replicate配置验证
    @property
    def replicate_configured(self) -> bool:
        """检查Replicate API是否已配置"""
        return bool(self.REPLICATE_API_TOKEN)

    # （已在“日志配置”中声明 ENABLE_IO_SNAPSHOTS / IO_SNAPSHOTS_MAX_CHARS）

    # 内容筛选配置（业务固定启用）
    CONTENT_SELECTION_ENABLED = True
    # 筛选步骤所用模型（与背景分析分离，便于走轻量模型降本提速）
    CONTENT_SELECTION_MODEL = os.getenv("CONTENT_SELECTION_MODEL", "openai/gpt-5-nano")
    CONTENT_SELECTION_MAX_COMMENTS = int(os.getenv("CONTENT_SELECTION_MAX_COMMENTS", "10"))
    CONTENT_SELECTION_MAX_TOKENS = _env_int("CONTENT_SELECTION_MAX_TOKENS", 10000)
    # 多样性约束：每个帖子最多保留的评论条数
    CONTENT_SELECTION_MAX_PER_POST = int(os.getenv("CONTENT_SELECTION_MAX_PER_POST", "5"))
    # LLM effort：控制筛选阶段的推理强度（minimal/low/medium/high）
    CONTENT_SELECTION_REASONING_EFFORT = os.getenv("CONTENT_SELECTION_REASONING_EFFORT", "low")

    # 背景分析 effort：控制用户背景画像阶段的推理强度（minimal/low/medium/high）
    LLM_CREDIBILITY_REASONING_EFFORT = os.getenv("LLM_CREDIBILITY_REASONING_EFFORT", "low")

    # =============================================================================
    # XAI Grok API 配置
    # =============================================================================
    XAI_API_KEY = os.getenv("XAI_API_KEY", "")
    GROK_MODEL = os.getenv("GROK_MODEL", "grok-3")
    GROK_MAX_COMMENTS = int(os.getenv("GROK_MAX_COMMENTS", "15"))

    # XAI Grok配置验证
    @property
    def grok_configured(self) -> bool:
        """检查XAI Grok API是否已配置"""
        return bool(self.XAI_API_KEY)

    # =============================================================================
    # 数据存储配置
    # =============================================================================
    # ===== 数据存储配置（PostgreSQL Only） =====
    DATABASE_URL = os.getenv("DATABASE_URL", "")
    ENABLE_DATABASE = os.getenv("ENABLE_DATABASE", "True").lower() == "true"
    
    # 数据库池配置（用于兼容性）
    DB_POOL_SIZE = int(os.getenv("DB_POOL_SIZE", "5"))
    DB_MAX_OVERFLOW = int(os.getenv("DB_MAX_OVERFLOW", "10"))
    DB_POOL_TIMEOUT = int(os.getenv("DB_POOL_TIMEOUT", "30"))

    # 本地开发数据库配置
    DB_HOST = os.getenv("DB_HOST", "localhost")
    DB_PORT = int(os.getenv("DB_PORT", "5432"))
    DB_NAME = os.getenv("DB_NAME", "cogbridges_db")
    DB_USER = os.getenv("DB_USER", "postgres")
    DB_PASSWORD = os.getenv("DB_PASSWORD", "")

    # 数据库连接池配置
    # DB_POOL_SIZE = int(os.getenv("DB_POOL_SIZE", "5"))
    # DB_MAX_OVERFLOW = int(os.getenv("DB_MAX_OVERFLOW", "10"))
    # DB_POOL_TIMEOUT = int(os.getenv("DB_POOL_TIMEOUT", "30"))

    # 数据库功能开关
    # ENABLE_DATABASE = os.getenv("ENABLE_DATABASE", "False").lower() == "true"
    # ENABLE_JSON_BACKUP = os.getenv("ENABLE_JSON_BACKUP", "True").lower() == "true"
    # 在数据库中存储完整原始JSON（原默认行为）。若Supabase存储空间有限，可置为False以节省空间。
    DB_STORE_RAW_DATA = os.getenv("DB_STORE_RAW_DATA", "True").lower() == "true"

    @property
    def database_url(self) -> str:
        """获取数据库连接URL"""
        if self.DATABASE_URL:
            return self.DATABASE_URL
        else:
            return f"postgresql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"

    @property
    def database_configured(self) -> bool:
        """检查数据库是否已配置"""
        return bool(self.DATABASE_URL or (self.DB_HOST and self.DB_NAME and self.DB_USER))

    # =============================================================================
    # 安全配置
    # =============================================================================
    ENABLE_CORS = os.getenv("ENABLE_CORS", "True").lower() == "true"
    # 默认允许本机及常见开发端口（5000为API，5001为前端，5173为Vite默认端口）
    _default_allowed = "http://localhost:5000,http://127.0.0.1:5000,http://localhost:5001,http://127.0.0.1:5001,http://localhost:5173,http://127.0.0.1:5173"
    ALLOWED_ORIGINS = os.getenv("ALLOWED_ORIGINS", _default_allowed).split(",")
    
    # =============================================================================
    # 开发配置
    # =============================================================================
    DEBUG_MODE = os.getenv("DEBUG_MODE", "True").lower() == "true"
    ENABLE_PROFILING = os.getenv("ENABLE_PROFILING", "False").lower() == "true"
    TEST_MODE = os.getenv("TEST_MODE", "False").lower() == "true"

    # =============================================================================
    # 邮件（SMTP）配置
    # =============================================================================
    EMAIL_SMTP_HOST = os.getenv("EMAIL_SMTP_HOST", "")
    EMAIL_SMTP_PORT = int(os.getenv("EMAIL_SMTP_PORT", "465"))
    EMAIL_SMTP_USER = os.getenv("EMAIL_SMTP_USER", "")
    EMAIL_SMTP_PASSWORD = os.getenv("EMAIL_SMTP_PASSWORD", "")
    EMAIL_FROM = os.getenv("EMAIL_FROM", EMAIL_SMTP_USER or "<EMAIL>")
    EMAIL_USE_TLS = os.getenv("EMAIL_USE_TLS", "False").lower() == "true"
    EMAIL_USE_SSL = os.getenv("EMAIL_USE_SSL", "True").lower() == "true"

    @property
    def email_configured(self) -> bool:
        try:
            return bool(self.EMAIL_SMTP_HOST and self.EMAIL_SMTP_USER and self.EMAIL_SMTP_PASSWORD)
        except Exception:
            return False

    # =============================================================================
    # Google OAuth Configuration
    # =============================================================================
    GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID", "743643699797-ukfpprouk93i2mf05054ejbj7kvpvq3d.apps.googleusercontent.com")
    GOOGLE_CLIENT_SECRET = os.getenv("GOOGLE_CLIENT_SECRET", "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")
    
    @property
    def google_oauth_configured(self) -> bool:
        try:
            return bool(self.GOOGLE_CLIENT_ID and self.GOOGLE_CLIENT_SECRET)
        except Exception:
            return False
    
    # =============================================================================
    # PayPal Configuration
    # =============================================================================
    PAYPAL_CLIENT_ID = os.getenv("PAYPAL_CLIENT_ID", "")
    PAYPAL_CLIENT_SECRET = os.getenv("PAYPAL_CLIENT_SECRET", "")
    PAYPAL_MODE = os.getenv("PAYPAL_MODE", "sandbox")  # sandbox or live
    PAYPAL_WEBHOOK_ID = os.getenv("PAYPAL_WEBHOOK_ID", "")
    FRONTEND_URL = os.getenv("FRONTEND_URL", "http://localhost:5173")

    # PayPal pricing configuration (in USD)
    PAYPAL_PLANS = {
        "250": {"points": 250, "price": "4.50", "description": "250 points (~12 searches)"},
        "500": {"points": 500, "price": "7.50", "description": "500 points (~25 searches)"},
        "1000": {"points": 1000, "price": "15.00", "description": "1,000 points (~50 searches)"}
    }

    @property
    def paypal_configured(self) -> bool:
        """Check if PayPal is configured"""
        return bool(self.PAYPAL_CLIENT_ID and self.PAYPAL_CLIENT_SECRET)
    
    # Points pricing configuration
    POINTS_PRICING = {
        "250": {"price": 4.5, "points": 250},
        "500": {"price": 7.5, "points": 500},
        "1000": {"price": 15.0, "points": 1000}
    }
    
    # Search cost configuration
    SEARCH_COST_POINTS = 20  # Cost per deep search: 20 points
    # Charging policy
    MIN_COMMENTS_FOR_CHARGE = 5  # Deduct points only when at least 5 comments are returned
    TRIAL_INITIAL_POINTS = 20  # Part of registration grant (kept for historical reasons)
    TRIAL_VERIFIED_POINTS = 20  # Another part of registration grant (kept for historical reasons)
    # Actual registration grants TRIAL_INITIAL_POINTS + TRIAL_VERIFIED_POINTS = 40 points
    ANONYMOUS_TRIAL_LIMIT = 1  # Anonymous users can only try once in their lifetime

    # =============================================================================
    # 配置验证方法
    # =============================================================================
    def validate_config(self) -> List[str]:
        """Validate configuration completeness, return list of error messages"""
        errors = []

        # Check required API configurations
        if not self.reddit_configured:
            errors.append("Reddit API not configured (REDDIT_CLIENT_ID, REDDIT_CLIENT_SECRET)")

        # Check Grok API configuration
        if not self.grok_configured:
            errors.append("XAI Grok API not configured (XAI_API_KEY)")

        # Check database configuration
        if self.ENABLE_DATABASE and not self.database_configured:
            errors.append("Database enabled but not properly configured (DATABASE_URL or DB_HOST/DB_NAME/DB_USER)")

        # Value range validation
        if self.REDDIT_TOP_COMMENTS_COUNT < 1:
            errors.append("Reddit comment count should be >= 1")

        if self.USER_HISTORY_COMMENTS_COUNT < 1:
            errors.append("User history comment count should be >= 1")

        if self.USER_HISTORY_POSTS_COUNT < 1:
            errors.append("User history post count should be >= 1")

        if self.GROK_MAX_COMMENTS < 1:
            errors.append("Grok max comment count should be >= 1")

        if self.DB_POOL_SIZE < 1:
            errors.append("Database connection pool size should be >= 1")

        if self.DB_MAX_OVERFLOW < 0:
            errors.append("Database connection pool max overflow should be >= 0")

        return errors
    
    def get_config_summary(self) -> dict:
        """获取配置摘要信息"""
        return {
            "reddit_configured": self.reddit_configured,
            "grok_configured": self.grok_configured,
            "grok_model": self.GROK_MODEL,
            "grok_max_comments": self.GROK_MAX_COMMENTS,
            "replicate_model": self.REPLICATE_MODEL,
            "content_selection_enabled": self.CONTENT_SELECTION_ENABLED,
            "content_selection_max_comments": self.CONTENT_SELECTION_MAX_COMMENTS,
            "database_configured": self.database_configured,
            "database_enabled": self.ENABLE_DATABASE,
            "json_logs_enabled": self.ENABLE_JSON_LOGS,
            "detailed_logs_enabled": self.SAVE_DETAILED_LOGS,
            "db_store_raw_data": self.DB_STORE_RAW_DATA,

            "comments_count": self.REDDIT_TOP_COMMENTS_COUNT,
            "user_history_comments": self.USER_HISTORY_COMMENTS_COUNT,
            "user_history_posts": self.USER_HISTORY_POSTS_COUNT,
            "debug_mode": self.DEBUG_MODE,
            "test_mode": self.TEST_MODE,
            "log_level": self.LOG_LEVEL
        }

# 创建全局配置实例
config = Config()

# 配置验证
def validate_startup_config():
    """Validate configuration at startup"""
    errors = config.validate_config()
    if errors:
        print("⚠️  Configuration validation failed:")
        for error in errors:
            print(f"  - {error}")
        print("\nPlease check .env file or environment variable configuration")
        return False
    
    print("✅ Configuration validation passed")
    return True

# 已移除直接执行入口，避免脚本行为污染生产与测试环境
