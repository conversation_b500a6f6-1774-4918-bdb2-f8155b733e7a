import { useState, useEffect, useRef } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import LoadingSteps from '../components/LoadingSteps'
import { api } from '../services/api'
import { getActiveSearch, setActiveSearch, updateActiveSearch, clearActiveSearch, isSearchInProgress } from '../services/searchSession'

const LoadingPage = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const [currentStep, setCurrentStep] = useState(0)
  const [progress, setProgress] = useState(0) // UI progress (smoothed + time-based)
  const [searchStatus, setSearchStatus] = useState('starting') // starting, searching, completed, error
  const [errorMessage, setErrorMessage] = useState('')
  const [searchResult, setSearchResult] = useState(null)
  const [etaSeconds, setEtaSeconds] = useState(null)
  const [backendProgress, setBackendProgress] = useState(0) // raw backend progress
  const [sessionId, setSessionId] = useState(null)

  const query = location.state?.query || 'Search query'

  // Search flow
  const startedRef = useRef(false)
  const clientSessionIdRef = useRef(null)
  const searchStartRef = useRef(null)
  const uiTimerRef = useRef(null)
  const searchCompletedRef = useRef(false)  // 添加标志跟踪搜索是否已完成

  // Assume an average 90s timeline for smoother UX
  const EXPECTED_TOTAL_MS = 90000
  // Four steps: search 6s, fetch Reddit 19s, user history 26s, LLM analysis 39s
  const STEP_DURATIONS_MS = [6000, 19000, 26000, 39000]
  const STEP_CUM_MS = [
    STEP_DURATIONS_MS[0],
    STEP_DURATIONS_MS[0] + STEP_DURATIONS_MS[1],
    STEP_DURATIONS_MS[0] + STEP_DURATIONS_MS[1] + STEP_DURATIONS_MS[2],
    EXPECTED_TOTAL_MS
  ]
  const STEP_CUM_PCT = STEP_CUM_MS.map(ms => (ms / EXPECTED_TOTAL_MS) * 100)

  useEffect(() => {
    if (startedRef.current) return
    if (searchCompletedRef.current) return

    // Prefer recovering existing active session
    const active = getActiveSearch()
    if (active && isSearchInProgress(active.status) && active.sessionId && active.query) {
      startedRef.current = true
      clientSessionIdRef.current = active.clientSessionId || `${Date.now()}_${Math.random().toString(36).slice(2, 8)}`
      setSessionId(active.sessionId)
      setSearchStatus('searching')
      searchStartRef.current = active.startedAt ? active.startedAt : Date.now()
      // Continue polling existing session
      resumePolling(active.sessionId, active.query)
      return
    }

    if (!location.state?.query) {
      navigate('/')
      return
    }

    startedRef.current = true
    // Stable id across StrictMode mounts to achieve idempotency on backend
    clientSessionIdRef.current = location.state?.sessionId || `${Date.now()}_${Math.random().toString(36).slice(2, 8)}`
    startSearch()
  }, [navigate])

  // Remove auto-cancel on unload/unmount; only manual cancel triggers cancellation
  useEffect(() => {
    return () => {}
  }, [])

  // Map backend percent to step based on the 90s timeline thresholds
  const mapBackendProgressToStep = (pct) => {
    if (pct < STEP_CUM_PCT[0]) return 0
    if (pct < STEP_CUM_PCT[1]) return 1
    if (pct < STEP_CUM_PCT[2]) return 2
    return 3
  }

  // Infer expected step from elapsed time on the fixed timeline
  const getTimeBasedStep = (elapsedMs) => {
    if (elapsedMs < STEP_CUM_MS[0]) return 0
    if (elapsedMs < STEP_CUM_MS[1]) return 1
    if (elapsedMs < STEP_CUM_MS[2]) return 2
    return 3
  }

  const startSearch = async () => {
    try {
      setSearchStatus('searching')
      searchStartRef.current = Date.now()

      // Kick off async search
      const searchResponse = await api.search(query, clientSessionIdRef.current)
      if (!searchResponse.success) {
        throw new Error(searchResponse.error || 'Search request failed')
      }

      const sessionId = searchResponse.session_id
      setSessionId(sessionId)
      setActiveSearch({
        query,
        clientSessionId: clientSessionIdRef.current,
        sessionId,
        status: 'searching',
        startedAt: searchStartRef.current
      })

      // Poll search progress
      const result = await api.pollSearchProgress(
        sessionId,
        (progressData) => {
          const backendPct = Number(progressData.progress || 0)
          const clamped = Math.max(0, Math.min(100, backendPct))
          setBackendProgress(clamped)
          updateActiveSearch({ progress: clamped, status: progressData.status || 'running' })
        },
        600000, // 10 min timeout
        1500 // base polling interval
      )

      // Completed
      setSearchResult(result)
      setProgress(100)
      setCurrentStep(3)
      setEtaSeconds(0)
      setSearchStatus('completed')
      searchCompletedRef.current = true  // 标记搜索已完成
      updateActiveSearch({ status: 'completed', finishedAt: Date.now() })
      clearActiveSearch()

      setTimeout(() => {
        navigate('/results', { 
          state: { 
            query,
            searchResult: result,
            searchTime: result.search_time || 0 // Use backend-calculated search time
          },
          replace: true  // 替换当前历史记录，而不是添加新的
        })
      }, 500)

    } catch (error) {
      console.error('Search failed:', error)
      setSearchStatus('error')
      // 针对 Reddit 拥堵（503）显示更友好的提示
      if (error?.status === 503) {
        setErrorMessage(error.message || 'A lot of people are searching right now. Please wait a moment and try again.')
      } else {
        setErrorMessage(error.message)
      }
      
      // Persist failure status to avoid redirect loop
      try {
        updateActiveSearch({ status: 'error', finishedAt: Date.now(), error: error?.message || 'Search failed' })
        clearActiveSearch()
      } catch (_) {}

      // Handle specific error cases without alert
      if (error.message && error.message.includes('Insufficient points')) {
        // 积分不足的情况现在已经在搜索前处理，这里只是备用
        // 不再跳转页面，让用户看到错误信息即可
        console.error('Insufficient points error:', error.message)
      } else if (error.message && error.message.includes('trial quota exhausted')) {
        setTimeout(() => {
          navigate('/register', { replace: true })
        }, 1500)
      } else {
        setTimeout(() => {
          navigate('/', { replace: true })
        }, 3000)
      }
    }
  }

  const resumePolling = async (sid, q) => {
    try {
      const result = await api.pollSearchProgress(
        sid,
        (progressData) => {
          const backendPct = Number(progressData.progress || 0)
          const clamped = Math.max(0, Math.min(100, backendPct))
          setBackendProgress(clamped)
          updateActiveSearch({ progress: clamped, status: progressData.status || 'running' })
        },
        600000,
        1500
      )
      setSearchResult(result)
      setProgress(100)
      setCurrentStep(3)
      setEtaSeconds(0)
      setSearchStatus('completed')
      searchCompletedRef.current = true
      updateActiveSearch({ status: 'completed', finishedAt: Date.now() })
      clearActiveSearch()
      setTimeout(() => {
        navigate('/results', {
          state: {
            query: q,
            searchResult: result,
            searchTime: result.search_time || 0 // Use backend-calculated search time
          },
          replace: true
        })
      }, 500)
    } catch (error) {
      console.error('Resume polling failed:', error)
      setSearchStatus('error')
      setErrorMessage(error.message || 'Failed to resume search')
    }
  }

  // UI ticker based on fixed 90s timeline
  // - Progress advances linearly up to 99% until complete
  // - Step is max(time-based step, backend-derived step)
  // - ETA is derived from the 90s expectation; after 90s, keep ~12s until completion
  useEffect(() => {
    if (searchStatus !== 'searching') return
    if (!searchStartRef.current) return

    const tick = () => {
      const now = Date.now()
      const elapsedMs = now - searchStartRef.current

      // Time-based expected progress (capped at 99%)
      const expectedPct = Math.min(99, (elapsedMs / EXPECTED_TOTAL_MS) * 100)
      // Respect faster backend progress but still cap at 99%
      const backendCapped = Math.min(99, Math.max(0, backendProgress))
      const candidatePct = Math.max(expectedPct, backendCapped)

      setProgress(prev => {
        const next = Math.max(prev, candidatePct)
        return searchStatus === 'completed' ? 100 : Math.min(next, 99)
      })

      // Step selection to avoid visual regressions
      const timeStep = getTimeBasedStep(elapsedMs)
      const backendStep = mapBackendProgressToStep(backendProgress)
      setCurrentStep(Math.max(timeStep, backendStep))

      // ETA calculation
      if (searchStatus !== 'completed') {
        if (elapsedMs <= EXPECTED_TOTAL_MS) {
          setEtaSeconds(Math.max(1, Math.ceil((EXPECTED_TOTAL_MS - elapsedMs) / 1000)))
        } else {
          setEtaSeconds(12)
        }
      }
    }

    // First run then interval
    tick()
    uiTimerRef.current = setInterval(tick, 500)
    return () => {
      if (uiTimerRef.current) clearInterval(uiTimerRef.current)
    }
  }, [searchStatus, backendProgress])

  // 错误态界面（更友好，不使用alert）
  if (searchStatus === 'error') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="text-center max-w-md w-full bg-white p-4 sm:p-6 rounded-xl border border-red-200 shadow">
          <div className="text-red-600 font-semibold mb-2 text-sm sm:text-base">Search failed</div>
          <div className="text-xs sm:text-sm text-gray-600 mb-4">{errorMessage || 'Something went wrong. Please try again.'}</div>
          <button
            onClick={() => navigate('/', { replace: true })}
            className="px-3 sm:px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors text-sm sm:text-base"
          >
            Back to home
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header - 优化移动端显示 */}
      <header className="bg-white shadow-sm border-b border-gray-200 px-3 sm:px-6 py-2 sm:py-4">
        <div className="max-w-4xl mx-auto">
          {/* 移动端垂直布局，桌面端水平布局 */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
            {/* Logo 区域 */}
            <div className="flex items-center space-x-2 sm:space-x-3">
              <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center flex-shrink-0">
                <span className="text-white font-bold text-xs sm:text-sm">C</span>
              </div>
              <div>
                <h1 className="text-base sm:text-lg font-semibold text-gray-800">CogBridges</h1>
                <p className="text-xs sm:text-sm text-gray-500 hidden sm:block">Working on your answers...</p>
              </div>
            </div>
            
            {/* 查询文本区域 - 优化移动端显示 */}
            <div className="text-left sm:text-right">
              <p className="text-xs sm:text-sm text-gray-600">Looking up</p>
              <p className="font-medium text-gray-800 text-sm sm:text-base line-clamp-2 break-words">"{query}"</p>
            </div>
          </div>
        </div>
      </header>

      {/* Main - 优化移动端间距 */}
      <main className="flex-1 flex items-center justify-center px-3 sm:px-6 py-4 sm:py-12">
        <div className="w-full max-w-3xl">
          {/* Overall progress - 优化移动端显示 */}
          <div className="mb-4 sm:mb-8">
            <div className="flex justify-between items-center mb-1 sm:mb-2">
              <span className="text-xs sm:text-sm font-medium text-gray-700">Progress</span>
              <span className="text-xs sm:text-sm text-gray-500">{Math.round(progress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-1.5 sm:h-2">
              <div 
                className="bg-gradient-to-r from-primary-500 to-primary-600 h-1.5 sm:h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            {etaSeconds != null && searchStatus !== 'completed' && (
              <div className="text-right text-[10px] sm:text-xs text-gray-500 mt-0.5 sm:mt-1">About {etaSeconds} s left</div>
            )}
          </div>

          {/* Steps (driven by backend progress) */}
          <LoadingSteps 
            currentStep={currentStep}
          />

          {/* Optional info card removed */}

          {/* Cancel button - 优化移动端显示 */}
          <div className="mt-6 sm:mt-8 text-center">
            <button
              onClick={async () => {
                try {
                  if (sessionId) {
                    await api.cancelSearch(sessionId)
                  }
                } catch (e) {
                  console.warn('Cancel failed:', e)
                } finally {
                  clearActiveSearch()
                  navigate('/', { replace: true })
                }
              }}
              className="px-4 sm:px-6 py-1.5 sm:py-2 text-gray-500 hover:text-gray-700 transition-colors text-sm sm:text-base touch-target"
            >
              Cancel and go home
            </button>
          </div>
        </div>
      </main>
    </div>
  )
}

export default LoadingPage 