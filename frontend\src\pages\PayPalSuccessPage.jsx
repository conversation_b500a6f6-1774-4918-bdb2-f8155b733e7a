import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { executePayPalPayment, getUserPoints } from '../services/api';

const PayPalSuccessPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [status, setStatus] = useState('processing'); // processing, success, error
  const [message, setMessage] = useState('Processing your payment...');
  const [pointsAdded, setPointsAdded] = useState(0);

  useEffect(() => {
    const processPayment = async () => {
      try {
        const paymentId = searchParams.get('paymentId');
        const payerId = searchParams.get('PayerID');

        if (!paymentId || !payerId) {
          setStatus('error');
          setMessage('Missing payment information. Please try again.');
          return;
        }

        // Execute the PayPal payment
        const result = await executePayPalPayment(paymentId, payerId);
        
        if (result.success) {
          setStatus('success');
          setMessage('Payment successful! Your points have been added to your account.');
          setPointsAdded(result.points_added || 0);
          
          // Refresh user points in the background
          getUserPoints().catch(console.error);
        } else {
          setStatus('error');
          setMessage(result.error || 'Payment processing failed. Please contact support.');
        }
      } catch (error) {
        console.error('Payment processing error:', error);
        setStatus('error');
        setMessage('An error occurred while processing your payment. Please contact support.');
      }
    };

    processPayment();
  }, [searchParams]);

  useEffect(() => {
    // Redirect to home after successful payment
    if (status === 'success') {
      const timer = setTimeout(() => {
        navigate('/');
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [status, navigate]);

  const getStatusIcon = () => {
    switch (status) {
      case 'processing':
        return (
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        );
      case 'success':
        return (
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
        );
      case 'error':
        return (
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
        );
      default:
        return null;
    }
  };

  const getStatusTitle = () => {
    switch (status) {
      case 'processing':
        return 'Processing Payment...';
      case 'success':
        return 'Payment Successful!';
      case 'error':
        return 'Payment Failed';
      default:
        return '';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
        {getStatusIcon()}
        
        <h1 className="text-2xl font-semibold mb-2">{getStatusTitle()}</h1>
        <p className="text-gray-600 mb-6">{message}</p>
        
        {status === 'success' && pointsAdded > 0 && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <p className="text-green-800 font-medium">
              +{pointsAdded} points added to your account
            </p>
          </div>
        )}
        
        {status === 'success' && (
          <p className="text-sm text-gray-500 mb-4">
            Redirecting to home page in a few seconds...
          </p>
        )}
        
        <div className="space-y-2">
          <button
            onClick={() => navigate('/')}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors duration-200"
          >
            Return to Home
          </button>
          
          {status === 'error' && (
            <button
              onClick={() => navigate('/purchase')}
              className="w-full bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-lg transition-colors duration-200"
            >
              Try Again
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default PayPalSuccessPage;
