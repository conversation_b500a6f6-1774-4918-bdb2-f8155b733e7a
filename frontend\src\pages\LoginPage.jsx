import { useState, useRef, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { api, getFriendlyErrorMessage } from '../services/api'
import { GoogleLogin } from '@react-oauth/google'

const LoginPage = () => {
  const navigate = useNavigate()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const [needVerification, setNeedVerification] = useState(false)
  const [info, setInfo] = useState('')
  const [sending, setSending] = useState(false)
  const [cooldown, setCooldown] = useState(0)
  const cooldownTimerRef = useRef(null)

  useEffect(() => {
    return () => {
      if (cooldownTimerRef.current) {
        clearInterval(cooldownTimerRef.current)
        cooldownTimerRef.current = null
      }
    }
  }, [])

  const onSubmit = async (e) => {
    e.preventDefault()
    console.log('🔐 Start login:', { email, passwordLength: password.length })
    setLoading(true)
    setError('')
    try {
      console.log('📤 Sending login request...')
      const res = await api.login(email, password)
      console.log('📥 Login response:', res)
      if (res?.need_verification) {
        console.log('⚠️ Email verification required')
        setNeedVerification(true)
        setInfo('Your email is not verified yet. Click below to send a verification code.')
        return
      }
      if (res?.success) {
        console.log('✅ Login successful, redirecting to home')
        navigate('/')
      } else {
        console.log('❌ Login failed:', res?.error)
        setError(res?.error || 'Login failed')
      }
    } catch (err) {
      console.log('💥 Login error:', err)
      // Show more friendly prompts based on error type
      const status = err?.response?.status
      if (err?.need_verification || status === 403) {
        setNeedVerification(true)
        setInfo('Your account is not verified. Click below to send a verification code to your email.')
        setError('Please verify your email to continue.')
      } else if (status === 401 || (err?.message || '').toLowerCase().includes('invalid email or password')) {
        setError('Email or password is incorrect')
      } else {
        setError(getFriendlyErrorMessage(err, 'Login failed. Please try again.'))
      }
    } finally {
      console.log('🏁 Login flow ended')
      setLoading(false)
    }
  }

  const handleGoogleSuccess = async (credentialResponse) => {
    console.log('🔐 Google login success:', credentialResponse)
    setLoading(true)
    setError('')
    try {
      const res = await api.loginWithGoogle(credentialResponse.credential)
      console.log('📥 Google login response:', res)
      if (res?.success) {
        console.log('✅ Google login successful, redirecting to home')
        navigate('/')
      } else {
        console.log('❌ Google login failed:', res?.error)
        setError(res?.error || 'Google login failed')
      }
    } catch (err) {
      console.log('💥 Google login error:', err)
      setError(getFriendlyErrorMessage(err, 'Google login failed. Please try again.'))
    } finally {
      setLoading(false)
    }
  }

  const handleGoogleError = () => {
    console.log('❌ Google login error')
    setError('Google login failed. Please try again.')
  }

  const sendCode = async () => {
    if (sending || cooldown > 0) return
    setSending(true)
    try {
      setInfo('')
      const r = await api.sendCode(email)
      if (r?.success) {
        // 发送成功后跳转到注册页面
        navigate('/register', { state: { email, codeSent: true } })
      } else {
        setInfo(r?.error || 'Failed to send verification code')
      }
    } catch (e) {
      setInfo(getFriendlyErrorMessage(e, 'Failed to send verification code'))
    } finally {
      setSending(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center px-4 py-8 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-6 sm:space-y-8">
        <div>
          <h2 className="mt-4 sm:mt-6 text-center text-2xl sm:text-3xl font-bold text-gray-900">
            Log in to your account
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Or{' '}
            <button
              onClick={() => navigate('/register')}
              className="font-medium text-primary-600 hover:text-primary-500"
            >
              create a new account
            </button>
          </p>
        </div>
        <form className="mt-6 sm:mt-8 space-y-4 sm:space-y-6" onSubmit={onSubmit}>
          {error && (
            <div className="rounded-md bg-red-50 p-3 sm:p-4">
              <div className="text-sm text-red-800">{error}</div>
            </div>
          )}
          {info && (
            <div className="rounded-md bg-blue-50 p-3 sm:p-4">
              <div className="text-sm text-blue-800">{info}</div>
            </div>
          )}
          <div className="rounded-md shadow-sm space-y-3 sm:space-y-4">
            <div>
              <label htmlFor="email" className="sr-only">
                Email address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                className="appearance-none rounded-lg relative block w-full px-3 py-2.5 sm:py-3 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 text-base"
                placeholder="Email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div>
              <label htmlFor="password" className="sr-only">
                Password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                required
                className="appearance-none rounded-lg relative block w-full px-3 py-2.5 sm:py-3 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 text-base"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={loading}
              className="group relative w-full flex justify-center py-2.5 sm:py-3 px-4 border border-transparent text-sm sm:text-base font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Logging in...' : 'Log in'}
            </button>
          </div>

          {needVerification && (
            <button
              type="button"
              onClick={sendCode}
              disabled={sending || cooldown > 0}
              className="w-full text-center py-2 sm:py-2.5 px-4 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {sending ? 'Sending...' : cooldown > 0 ? `Resend code in ${cooldown}s` : 'Send verification code'}
            </button>
          )}

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-gray-50 text-gray-500">Or continue with</span>
            </div>
          </div>

          <div className="w-full flex justify-center">
            <GoogleLogin
              onSuccess={handleGoogleSuccess}
              onError={handleGoogleError}
              theme="outline"
              size="large"
              logo_alignment="left"
            />
          </div>
        </form>
      </div>
    </div>
  )
}

export default LoginPage
