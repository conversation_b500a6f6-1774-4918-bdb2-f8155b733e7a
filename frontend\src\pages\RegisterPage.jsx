import { useState, useRef, useEffect, useMemo } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { api, getFriendlyErrorMessage } from '../services/api'
import { GoogleLogin } from '@react-oauth/google'

const MIN_LEN = 8
function computeStrength(pwd, email) {
  const text = pwd || ''
  const lengthScore = text.length >= MIN_LEN ? 1 : 0
  const lower = /[a-z]/.test(text)
  const upper = /[A-Z]/.test(text)
  const digit = /[0-9]/.test(text)
  const symbol = /[^A-Za-z0-9]/.test(text)
  const variety = [lower, upper, digit, symbol].filter(Boolean).length
  const local = (email || '').split('@')[0] || ''
  const containsEmail = local && text.toLowerCase().includes(local.toLowerCase())
  const common = ['password','123456','qwerty','111111','abc123','letmein','welcome','admin','p@ssw0rd','passw0rd','12345678','iloveyou']
  const isCommon = common.some(w => text.toLowerCase().includes(w))
  const ok = lengthScore && variety >= 2 && !containsEmail && !isCommon
  let level = 'weak'
  if (ok && text.length >= 12 && variety >= 3) level = 'strong'
  else if (ok) level = 'medium'
  let reason = ''
  if (!lengthScore) reason = `Use at least ${MIN_LEN} characters`
  else if (variety < 2) reason = 'Include any two of lowercase/uppercase/digit/symbol'
  else if (containsEmail) reason = 'Should not contain your email name'
  else if (isCommon) reason = 'Too common'
  return { ok, level, reason }
}

const RegisterPage = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const [email, setEmail] = useState(location.state?.email || '')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const [needVerification, setNeedVerification] = useState(location.state?.codeSent || false)
  const [code, setCode] = useState('')
  const [codeMsg, setCodeMsg] = useState(location.state?.codeSent ? 'Verification code sent. Please check your inbox' : '')
  const [sending, setSending] = useState(false)
  const [cooldown, setCooldown] = useState(location.state?.codeSent ? 30 : 0)
  const cooldownTimerRef = useRef(null)

  useEffect(() => {
    // 如果从登录页跳转过来且已发送验证码，启动倒计时
    if (location.state?.codeSent && cooldown > 0) {
      cooldownTimerRef.current = setInterval(() => {
        setCooldown(prev => {
          if (prev <= 1) {
            clearInterval(cooldownTimerRef.current)
            cooldownTimerRef.current = null
            return 0
          }
          return prev - 1
        })
      }, 1000)
    }
    return () => {
      if (cooldownTimerRef.current) {
        clearInterval(cooldownTimerRef.current)
        cooldownTimerRef.current = null
      }
    }
  }, [])

  const handleGoogleSuccess = async (credentialResponse) => {
    console.log('🔐 Google register success:', credentialResponse)
    setLoading(true)
    setError('')
    try {
      const res = await api.loginWithGoogle(credentialResponse.credential)
      console.log('📥 Google register response:', res)
      if (res?.success) {
        console.log('✅ Google register successful, redirecting to home')
        navigate('/')
      } else {
        console.log('❌ Google register failed:', res?.error)
        setError(res?.error || 'Google sign up failed')
      }
    } catch (err) {
      console.log('💥 Google register error:', err)
      setError(getFriendlyErrorMessage(err, 'Google sign up failed. Please try again.'))
    } finally {
      setLoading(false)
    }
  }

  const handleGoogleError = () => {
    console.log('❌ Google register error')
    setError('Google sign up failed. Please try again.')
  }

  const strength = useMemo(() => computeStrength(password, email), [password, email])

  const onSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')
    try {
      const res = await api.register(email, password, code)
      if (res?.need_verification) {
        setNeedVerification(true)
        setCodeMsg((res?.message || 'Verification code sent. Please check your inbox') + (res?.dev_code ? ` (DEV code: ${res.dev_code})` : ''))
        return
      }
      if (res?.success) navigate('/')
      else setError(res?.error || 'Sign up failed')
    } catch (err) {
      setError(getFriendlyErrorMessage(err, 'Sign up failed'))
    } finally {
      setLoading(false)
    }
  }

  const onSendCode = async () => {
    if (sending || cooldown > 0) return
    setSending(true)
    try {
      setCodeMsg('')
      const r = await api.sendCode(email)
      if (r?.success) setCodeMsg('Verification code sent. Please check your inbox')
      else setCodeMsg(r?.error || 'Failed to send code')
      setNeedVerification(true)
    } catch (e) {
      setCodeMsg(getFriendlyErrorMessage(e, 'Failed to send code'))
    } finally {
      setSending(false)
      // start cooldown
      setCooldown(30)
      if (cooldownTimerRef.current) clearInterval(cooldownTimerRef.current)
      cooldownTimerRef.current = setInterval(() => {
        setCooldown(prev => {
          if (prev <= 1) {
            clearInterval(cooldownTimerRef.current)
            cooldownTimerRef.current = null
            return 0
          }
          return prev - 1
        })
      }, 1000)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center px-4 py-8 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-6 sm:space-y-8">
        <div>
          <h2 className="mt-4 sm:mt-6 text-center text-2xl sm:text-3xl font-bold text-gray-900">
            Create a new account
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Or{' '}
            <button
              onClick={() => navigate('/login')}
              className="font-medium text-primary-600 hover:text-primary-500"
            >
              log in to existing account
            </button>
          </p>
        </div>
        {error && (
          <div className="rounded-md bg-red-50 p-3 sm:p-4">
            <div className="text-sm text-red-800">{error}</div>
          </div>
        )}
        <form onSubmit={onSubmit} className="mt-6 sm:mt-8 space-y-4 sm:space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
            <input type="email" value={email} onChange={e => setEmail(e.target.value)}
                   className="appearance-none rounded-lg relative block w-full px-3 py-2.5 sm:py-3 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 text-base" required />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
            <input type="password" value={password} onChange={e => setPassword(e.target.value)}
                   className="appearance-none rounded-lg relative block w-full px-3 py-2.5 sm:py-3 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 text-base" required />
            {password && (
              <div className="mt-2 text-xs">
                <div className={`inline-block px-2 py-0.5 rounded ${strength.level==='strong'?'bg-green-100 text-green-700':strength.level==='medium'?'bg-yellow-100 text-yellow-700':'bg-red-100 text-red-700'}`}>
                  Strength: {strength.level}
                </div>
                {!strength.ok && <span className="ml-2 text-gray-600">Hint: {strength.reason}</span>}
              </div>
            )}
            <div className="text-xs text-gray-500 mt-1">At least {MIN_LEN} characters. Include any two of lowercase/uppercase/digit/symbol. Avoid using your email name or common passwords.</div>
          </div>
          {needVerification && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Email verification code</label>
              <div className="flex gap-2">
                <input type="text" value={code} onChange={e => setCode(e.target.value)}
                       className="flex-1 appearance-none rounded-lg px-3 py-2.5 sm:py-3 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-base" placeholder="6-digit code" />
                <button type="button" onClick={onSendCode} disabled={sending || cooldown > 0 || !email}
                        className="px-3 sm:px-4 py-2 sm:py-2.5 bg-gray-100 hover:bg-gray-200 rounded-lg border disabled:opacity-60 text-sm whitespace-nowrap">
                  {sending ? 'Sending...' : (cooldown > 0 ? `${cooldown}s` : 'Send')}
                </button>
              </div>
              {codeMsg && <div className="text-xs text-gray-600 mt-1">{codeMsg}</div>}
            </div>
          )}
          <button type="submit" disabled={loading || (!needVerification && !strength.ok)}
                  className="group relative w-full flex justify-center py-2.5 sm:py-3 px-4 border border-transparent text-sm sm:text-base font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed">
            {loading ? 'Submitting...' : (needVerification ? 'Submit code and complete sign-up' : 'Sign up')}
          </button>
          
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-gray-50 text-gray-500">Or continue with</span>
            </div>
          </div>
          
          <div className="w-full flex justify-center">
            <GoogleLogin
              onSuccess={handleGoogleSuccess}
              onError={handleGoogleError}
              useOneTap
              theme="outline"
              size="large"
              text="signup_with"
              logo_alignment="left"
            />
          </div>
        </form>
      </div>
    </div>
  )
}

export default RegisterPage
