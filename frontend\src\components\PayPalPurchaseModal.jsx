import React, { useState, useEffect } from 'react';
import { createPayPalOrder, executePayPalPayment } from '../services/api';

const PayPalPurchaseModal = ({ isOpen, onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [paypalLoaded, setPaypalLoaded] = useState(false);

  const priceOptions = [
    {
      id: '250',
      points: 250,
      price: 4.50,
      label: '$4.50',
      description: '250 points (~12 searches)'
    },
    {
      id: '500',
      points: 500,
      price: 7.50,
      label: '$7.50',
      description: '500 points (~25 searches)',
      popular: true
    },
    {
      id: '1000',
      points: 1000,
      price: 15.00,
      label: '$15.00',
      description: '1,000 points (~50 searches)'
    }
  ];

  // Load PayPal SDK
  useEffect(() => {
    if (isOpen && !paypalLoaded) {
      const script = document.createElement('script');
      script.src = `https://www.paypal.com/sdk/js?client-id=${import.meta.env.VITE_PAYPAL_CLIENT_ID || 'test'}&currency=USD&locale=en_US`;
      script.async = true;
      script.onload = () => setPaypalLoaded(true);
      script.onerror = () => setError('Failed to load PayPal SDK');
      document.body.appendChild(script);

      return () => {
        // Cleanup script if component unmounts (be defensive for test stubs)
        try {
          if (script && script.parentNode) {
            script.parentNode.removeChild(script);
          } else if (script && typeof document.body.contains === 'function') {
            if (document.body.contains(script)) {
              document.body.removeChild(script);
            }
          }
        } catch (_) {
          // ignore cleanup errors in tests
        }
      };
    }
  }, [isOpen, paypalLoaded]);

  const handlePayPalPayment = async (planId) => {
    try {
      setLoading(true);
      setError(null);

      // Create PayPal order
      const orderResult = await createPayPalOrder(planId);
      if (!orderResult.success) {
        throw new Error(orderResult.error || 'Failed to create PayPal order');
      }

      // Redirect to PayPal for approval
      window.location.href = orderResult.approval_url;
    } catch (err) {
      setError(err.message);
      setLoading(false);
    }
  };

  const renderPayPalButton = (option) => {
    // Fallback: even if PayPal SDK hasn't loaded, allow creating order directly
    const button = (
      <button
        onClick={() => handlePayPalPayment(option.id)}
        disabled={loading}
        className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
      >
        {loading ? (
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
        ) : (
          <>
            <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="currentColor">
              <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.541c-.013.076-.026.175-.041.254-.93 4.778-4.005 6.41-7.97 6.41h-1.97c-.524 0-.968.382-1.05.9l-1.338 8.48a.641.641 0 0 0 .633.74h4.25c.524 0 .968-.382 1.05-.9l.44-2.79h1.87c3.73 0 6.64-1.51 7.49-5.89.32-1.64.17-3.01-.76-3.96z"/>
            </svg>
            Pay with PayPal
          </>
        )}
      </button>
    )

    if (!paypalLoaded || !window.paypal) {
      return <div className="paypal-button-container">{button}</div>
    }

    return <div className="paypal-button-container">{button}</div>
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4" data-testid="paypal-modal-overlay">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto" data-testid="paypal-modal-content">
        <div className="p-4 sm:p-6">
          <div className="flex justify-between items-center mb-4 sm:mb-6">
            <h2 className="text-lg sm:text-xl font-semibold" data-testid="modal-title">Purchase Points</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-xl sm:text-2xl"
              data-testid="close-button"
            >
              ×
            </button>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          <div className="space-y-3 sm:space-y-4" data-testid="price-options">
            {priceOptions.map((option) => (
              <div key={option.id} className="border rounded-lg p-3 sm:p-4 relative" data-testid={`price-option-${option.id}`}>
                {option.popular && (
                  <div className="absolute -top-2 left-4 bg-blue-600 text-white text-xs px-2 py-1 rounded">
                    Most Popular
                  </div>
                )}
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="text-lg sm:text-xl font-bold" data-testid={`price-label-${option.id}`}>{option.label}</span>
                      <span className="text-sm sm:text-base text-gray-600" data-testid={`points-label-${option.id}`}>{option.points} points</span>
                    </div>
                    <p className="text-xs sm:text-sm text-gray-600 mt-0.5 sm:mt-1" data-testid={`description-${option.id}`}>{option.description}</p>
                  </div>
                </div>
                {renderPayPalButton(option)}
              </div>
            ))}
          </div>

          <div className="mt-4 sm:mt-6 pt-3 sm:pt-4 border-t">
            <p className="text-xs text-gray-500 text-center" data-testid="security-message">
              Payment processed securely by PayPal. Multiple payment methods accepted.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PayPalPurchaseModal;
