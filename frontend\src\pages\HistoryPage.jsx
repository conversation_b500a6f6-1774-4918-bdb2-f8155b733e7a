import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { ArrowLeft, Clock, Search, Trash2, AlertCircle } from 'lucide-react'
import { api, authStore } from '../services/api'

const HistoryPage = () => {
  const navigate = useNavigate()
  const [searchHistory, setSearchHistory] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  // Load data
  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      setError(null)
      
      try {
        // 首先检查认证状态，不依赖 authStore
        let authenticated = false
        try {
          const me = await api.me()
          authenticated = !!me?.authenticated
          setIsAuthenticated(authenticated)
          // 同步更新 authStore
          authStore.authenticated = authenticated
          authStore.user = me?.user || null
        } catch (err) {
          // 认证失败，用户未登录
          authenticated = false
          setIsAuthenticated(false)
        }
        
        // If not logged in, show empty
        if (!authenticated) {
          setSearchHistory([])
          return
        }
        
        const historyData = await api.getSearchHistory()
        setSearchHistory(historyData)
      } catch (err) {
        console.error('Failed to load data:', err)
        setError(`Failed to load: ${err.message}`)

        // Do not use mock data; show empty
        setSearchHistory([])
      } finally {
        setLoading(false)
      }
    }
    
    loadData()
  }, [])

  const formatTimeAgo = (timestamp) => {
    if (!timestamp) return 'Unknown time'

    const toDate = (input) => {
      if (typeof input === 'number' && isFinite(input)) {
        const num = Number(input)
        const ms = num > 1e12 ? num : num * 1000
        const d = new Date(ms)
        return isNaN(d.getTime()) ? null : d
      }
      if (typeof input === 'string') {
        let s = input.trim()
        if (s.includes(' ')) s = s.replace(' ', 'T')
        s = s.replace(/(\.\d{3})\d+/, '$1')
        if (!/[zZ]|[+-]\d{2}:?\d{2}$/.test(s)) {
          s += 'Z'
        }
        const d = new Date(s)
        if (!isNaN(d.getTime())) return d
      }
      return null
    }

    const date = toDate(timestamp)
    if (!date) return 'Unknown time'

    const now = new Date()
    let diffMs = now - date
    if (diffMs < 0) diffMs = 0
    const seconds = Math.floor(diffMs / 1000)
    if (seconds <= 30) return 'Just now'

    const rtf = (typeof Intl !== 'undefined' && Intl.RelativeTimeFormat)
      ? new Intl.RelativeTimeFormat('en', { numeric: 'auto' })
      : null

    const divisions = [
      { amount: 60, unit: 'second' },
      { amount: 60, unit: 'minute' },
      { amount: 24, unit: 'hour' },
      { amount: 7, unit: 'day' },
      { amount: 4.34524, unit: 'week' },
      { amount: 12, unit: 'month' },
      { amount: Infinity, unit: 'year' },
    ]

    let duration = -seconds
    for (const division of divisions) {
      if (Math.abs(duration) < division.amount) {
        if (rtf) return rtf.format(Math.round(duration), division.unit)
        const abs = Math.round(Math.abs(duration))
        const shortUnits = { second: 's', minute: 'm', hour: 'h', day: 'd', week: 'w', month: 'mo', year: 'y' }
        return `${abs}${shortUnits[division.unit]} ago`
      }
      duration /= division.amount
    }

    return 'Just now'
  }

  const handleRerunSearch = async (query) => {
    try {
      setError(null)
      
      navigate('/loading', {
        state: {
          query,
          sessionId: Date.now().toString(),
          timestamp: Date.now()
        }
      })
    } catch (error) {
      setError(`Search failed: ${error.message}`)
    }
  }

  const handleOpenHistoryItem = async (item) => {
    try {
      setLoading(true)
      // Load full session from backend
      const session = await api.getResultDetail(item.id)
      if (!session) throw new Error('No session data')

      // Compatibility with different storage structures
      const searchResult = (session?.search_result && session.search_result.reddit_posts)
        ? session.search_result
        : session

      const queryField = searchResult?.query
      const queryText = (typeof queryField === 'string')
        ? queryField
        : (queryField?.query || item.query || '')

      const totalTimeSec = session?.statistics?.total_time || session?.total_time || 0

      // Only pass minimal fields to avoid large navigation state
      navigate('/results', {
        state: {
          sessionId: item.id,
          query: queryText,
          searchTime: Math.round(totalTimeSec * 1000)
        }
      })
    } catch (error) {
      console.error('Failed to open history item:', error)
      setError('Failed to open history: ' + error.message)
    } finally {
      setLoading(false)
    }
  }

  const handleRemoveHistory = async (id) => {
    try {
      // Optimistic local update
      setSearchHistory(prev => prev.filter(item => item.id !== id))
      // Call API to remove
      await api.removeHistory(id)
    } catch (error) {
      console.error('Failed to remove history:', error)
      // Rollback on failure: reload history from server
      try {
        const historyData = await api.getSearchHistory()
        setSearchHistory(historyData)
        setError('Failed to delete. Please try again.')
      } catch (e) {
        setError('Failed to sync history. Please refresh the page.')
      }
    }
  }

  // Bookmarks/exports removed

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 py-3 sm:py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/')}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              
              <div>
                <h1 className="text-lg sm:text-xl font-semibold text-gray-800">My Activity</h1>
                <p className="text-xs sm:text-sm text-gray-500">Search history</p>
              </div>
            </div>
            {/* Export removed */}
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 py-6">
        {/* Error banner */}
        {error && (
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg flex items-center space-x-3">
            <AlertCircle className="w-5 h-5 text-yellow-500" />
            <div>
              <p className="text-yellow-800 font-medium">Notice</p>
              <p className="text-yellow-700 text-sm">{error}</p>
            </div>
          </div>
        )}

        {/* Tabs simplified to count only */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="flex items-center justify-center px-6 py-4 text-gray-700">
            <Clock className="w-4 h-4 mr-2" />
            <span>Search history</span>
            <span className="ml-2 bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded-full">
              {searchHistory.length}
            </span>
          </div>
        </div>

        {/* List */}
        <div className="space-y-4">
            {searchHistory.length === 0 ? (
              <div className="bg-white rounded-lg border border-gray-200 p-12 text-center">
                <Clock className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-800 mb-2">No history yet</h3>
                <p className="text-gray-500 mb-4">Start your first smart search</p>
                <button
                  onClick={() => navigate('/')}
                  className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
                >
                  Start searching
                </button>
              </div>
            ) : (
              searchHistory.map((item) => (
                <div
                  key={item.id}
                  className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => handleOpenHistoryItem(item)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h3 className="text-lg font-medium text-gray-800 mb-2">
                        "{item.query}"
                      </h3>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        {/* Prefer created_at from backend DB if available, otherwise use timestamp */}
                        <span>{formatTimeAgo(item.created_at || item.timestamp)}</span>
                        {item.resultsCount && (
                          <>
                            <span>•</span>
                            <span>{item.resultsCount} results</span>
                          </>
                        )}
                        {item.searchTime && (
                          <>
                            <span>•</span>
                            <span>{item.searchTime} s</span>
                          </>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={(e) => { e.stopPropagation(); handleRerunSearch(item.query) }}
                        className="flex items-center space-x-2 px-4 py-2 text-primary-600 hover:text-primary-700 hover:bg-primary-50 rounded-lg transition-colors disabled:opacity-50"
                      >
                        <Search className="w-4 h-4" />
                        <span>Search again</span>
                      </button>
                      
                      <button
                        onClick={(e) => { e.stopPropagation(); handleRemoveHistory(item.id) }}
                        className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        title="Remove"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

        {/* Collections removed */}
      </div>
    </div>
  )
}

export default HistoryPage 