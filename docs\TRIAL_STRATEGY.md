# Trial Strategy Documentation

## Overview

To prevent unlimited free usage by non-logged-in users while providing a good user experience, we implement the following trial strategy:

## User Types and Permissions

### 1. Non-logged-in Users
- **Trial limit**: Only 1 trial per lifetime
- **Implementation**: Based on Cookie (`anonymous_trial_used`)
- **Cookie validity**: 1 year (365 days)
- **Reason for limit**: Prevent unlimited trials by clearing cookies

### 2. Registered Users
- **Initial points**: 40 points (2 searches)
- **How to obtain**: Must verify email upon registration, get 40 points all at once
- **After using up**: Must purchase points, no other free quota

## Technical Implementation

### Backend Implementation (api/blueprints_search.py)

1. **Non-logged-in check**:
```python
if not owner_uid:
    anonymous_trial_used = request.cookies.get('anonymous_trial_used')
    if anonymous_trial_used == 'true':
        return jsonify({
            "success": False,
            "error": "Trial limit reached. Please register to continue",
            "need_login": True
        }), 403
```

2. **Cookie setting**:
```python
resp.set_cookie('anonymous_trial_used', 'true', 
                max_age=365*24*60*60,  # 1 year
                httponly=True, 
                samesite='Lax')
```

3. **Registered user check**:
```python
# Simple points check
if user.points_balance < required_points:
    return jsonify({
        "success": False, 
        "error": "Insufficient points",
        "required": required_points,
        "balance": user.points_balance
    }), 402
```

### Frontend Handling (LoadingPage.jsx)

```javascript
if (error.message && error.message.includes('Trial limit reached')) {
  alert('Trial limit reached. Please register to continue')
  navigate('/register')
}
```

## User Flow

### New User Experience Flow
1. Visit website → Can try 1 search
2. Second search → Prompt to register
3. Register (must verify email) → Get 40 points (2 searches)
4. Use up 40 points → Must purchase points package

### Paid Conversion Path
1. **Trial experience** → Feel product value (1 time)
2. **Registration barrier** → Email verification registration (get 2 times)
3. **Paid conversion** → Must pay after using 3 times

## Summary

- **Total free times**: Maximum 3 times (1 non-logged-in + 2 registered)
- **No daily trial**: Must pay after using up
- **Simple and direct**: No complex trial rules
- **Clear conversion**: Trial → Register → Pay