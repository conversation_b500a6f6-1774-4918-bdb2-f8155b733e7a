"""
CogBridges Search - 数据存储服务（PostgreSQL Only）
统一通过 DatabaseService 进行持久化与读取。
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
import hashlib
import uuid

from config import config
from models.search_models import SearchResult
from utils.logger_utils import get_logger
from services.database_service import DatabaseService


class DataService:
    """数据存储服务类"""
    
    def __init__(self):
        """初始化数据存储服务"""
        self.logger = get_logger(__name__)

        # 初始化数据库存储服务（唯一实现）
        self.storage_service: Optional[DatabaseService] = None
        self.database_service: Optional[DatabaseService] = None
        try:
            self.storage_service = DatabaseService()
            self.database_service = self.storage_service
            self.logger.info("数据库存储服务已启用")
        except Exception as e:
            # 明确失败，业务必须依赖数据库
            self.logger.error(f"数据库存储服务初始化失败: {e}")
            raise
    
    def cleanup(self):
        """清理数据服务资源"""
        if self.storage_service:
            self.storage_service.cleanup()
            self.logger.info("数据服务资源已清理")
    
    def __del__(self):
        """析构函数 - 确保资源被清理"""
        self.cleanup()
    
    def generate_session_id(self, query: str) -> str:
        """
        生成会话ID
        
        Args:
            query: 搜索查询
            
        Returns:
            会话ID
        """
        # 使用UTC时间+查询哈希+随机段生成高唯一性ID，避免热点同秒冲突
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S_%f")
        query_hash = hashlib.md5(query.encode('utf-8')).hexdigest()[:6]
        random_hex = uuid.uuid4().hex[:8]
        return f"{timestamp}_{query_hash}_{random_hex}"

    # 仅数据库实现，移除 JSON 索引/备份相关工具方法
    
    def save_search_result(self, search_result: SearchResult, session_id: str = None) -> str:
        """保持兼容的接口（不再写入JSON）。"""
        if not session_id:
            session_id = self.generate_session_id(search_result.query.query)
        self.logger.info(f"搜索结果准备持久化到数据库: {session_id}")
        # 上游会通过 save_complete_session 统一入库
        return f"database:{session_id}"
    
    def save_reddit_data(self, reddit_data: Dict[str, Any], session_id: str) -> str:
        """保持兼容的接口（不再写入JSON）。"""
        self.logger.info(f"Reddit数据将通过 save_complete_session 一并持久化: {session_id}")
        return f"database:{session_id}"
    
    def save_complete_session(
        self,
        session_id: str,
        data_to_save: Dict[str, Any]
    ) -> str:
        """
        保存完整的会话数据（支持双存储）

        Args:
            session_id: 会话ID
            data_to_save: 要保存的数据字典

        Returns:
            保存的文件路径
        """
        # 序列化数据（保持字段格式）
        serializable_data = self._make_serializable(data_to_save)

        # 仅保存到数据库
        if not (self.storage_service and self.storage_service.is_available()):
            raise RuntimeError("数据库存储服务不可用")

        # 从数据中提取搜索结果
        search_result_data = serializable_data.get('search_result', {})
        search_result = self._reconstruct_search_result(search_result_data)
        reddit_data = serializable_data.get('reddit_data', {})
        llm_analysis = serializable_data.get('llm_analysis', {})

        owner_user_id = None
        try:
            raw_owner = serializable_data.get('_owner_user_id')
            if raw_owner is not None:
                owner_user_id = int(raw_owner)
        except Exception:
            owner_user_id = None

        db_success = self.storage_service.save_search_session(
            session_id=session_id,
            search_result=search_result,
            reddit_data=reddit_data,
            raw_data=serializable_data,
            llm_analysis=llm_analysis,
            owner_user_id=owner_user_id
        )

        if not db_success:
            raise RuntimeError("会话数据保存到数据库失败")

        self.logger.info(f"会话数据已保存到数据库: {session_id}")
        return f"database:{session_id}"

    def _reconstruct_search_result(self, search_result_data: Dict[str, Any]) -> SearchResult:
        """
        从字典数据重构SearchResult对象

        Args:
            search_result_data: 搜索结果数据字典

        Returns:
            SearchResult对象
        """
        from models.search_models import SearchQuery

        # 重构SearchQuery（兼容 query 为字符串或对象）
        raw_query = search_result_data.get('query', {})
        if isinstance(raw_query, dict):
            query_str = raw_query.get('query', '')
            ts_raw = raw_query.get('timestamp', datetime.now().isoformat())
            search_type = raw_query.get('search_type', 'reddit')
            max_results = raw_query.get('max_results', 5)
            site_filter = raw_query.get('site_filter', 'site:reddit.com')
        else:
            query_str = str(raw_query) if raw_query is not None else ''
            ts_raw = datetime.now().isoformat()
            search_type = 'reddit'
            max_results = 5
            site_filter = 'site:reddit.com'

        try:
            ts = datetime.fromisoformat(ts_raw)
        except Exception:
            ts = datetime.now()

        search_query = SearchQuery(
            query=query_str,
            timestamp=ts,
            search_type=search_type,
            max_results=max_results,
            site_filter=site_filter
        )

        # 重构SearchResult
        search_result = SearchResult(
            query=search_query,
            success=search_result_data.get('success', True),
            error_message=search_result_data.get('error_message')
        )

        search_result.google_results = []

        return search_result
    
    def load_session_data(self, session_id: str) -> Optional[Dict[str, Any]]:
        """加载会话数据（仅数据库）。"""
        if self.storage_service and self.storage_service.is_available():
            try:
                data = self.storage_service.load_search_session(session_id)
                if data:
                    self.logger.info(f"从数据库加载会话数据成功: {session_id}")
                    return data
            except Exception as e:
                self.logger.error(f"从数据库加载失败: {e}")
        return None
    
    def list_sessions(self, limit: int = 50, owner_user_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """列出最近的会话（仅数据库）。"""
        sessions: List[Dict[str, Any]] = []
        if not (self.storage_service and self.storage_service.is_available()):
            return sessions
        try:
            db_sessions = self.storage_service.list_search_sessions(limit=limit, owner_user_id=owner_user_id)
            for session_data in db_sessions:
                sessions.append({
                    "session_id": session_data.get("id"),
                    "timestamp": session_data.get("timestamp", ""),
                    "query": session_data.get("query", ""),
                    "source": "database",
                    "google_results_count": session_data.get("google_results_count", 0),
                    "reddit_posts_count": session_data.get("reddit_posts_count", 0),
                    "reddit_comments_count": session_data.get("reddit_comments_count", 0),
                    "success": session_data.get("success", True),
                    "created_at": session_data.get("created_at", ""),
                    "updated_at": session_data.get("updated_at", ""),
                    "owner_user_id": session_data.get("owner_user_id"),
                })
            self.logger.info(f"从数据库获取到 {len(sessions)} 个会话")
        except Exception as e:
            self.logger.error(f"从数据库获取会话失败: {e}")
        return sessions[:limit]
    
    def delete_session(self, session_id: str) -> bool:
        """删除会话数据（仅数据库）。"""
        if not (self.storage_service and self.storage_service.is_available()):
            return False
        try:
            db_success = self.storage_service.delete_search_session(session_id)
            if db_success:
                self.logger.info(f"会话数据删除成功: {session_id}")
                return True
            self.logger.warning(f"未找到会话数据: {session_id}")
            return False
        except Exception as e:
            self.logger.error(f"删除会话失败: {e}")
            return False
    
    def _make_serializable(self, data: Any) -> Any:
        """
        将数据转换为可序列化格式
        
        Args:
            data: 原始数据
            
        Returns:
            可序列化的数据
        """
        if isinstance(data, dict):
            return {key: self._make_serializable(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._make_serializable(item) for item in data]
        elif hasattr(data, 'to_dict'):
            return data.to_dict()
        elif isinstance(data, (datetime,)):
            return data.isoformat()
        else:
            return data
    
    def get_storage_statistics(self) -> Dict[str, Any]:
        """获取存储统计信息（仅数据库）。"""
        statistics: Dict[str, Any] = {
            "storage_type": "database",
            "database_enabled": config.ENABLE_DATABASE,
        }
        if self.storage_service and self.storage_service.is_available():
            try:
                db_stats = self.storage_service.get_database_statistics()
                statistics["database"] = db_stats
            except Exception as e:
                self.logger.warning(f"获取数据库统计失败: {e}")
                statistics["database"] = {"error": str(e)}
        return statistics

    # 迁移功能已移除（PostgreSQL-only）


# 已移除直接执行入口，避免脚本行为污染生产与测试环境
