import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./vitest.setup.ts', './src/setupTests.ts'],
    globals: true,
    css: false,
    include: ['src/**/*.{test,spec}.{js,jsx,ts,tsx}'],
    exclude: ['tests', 'e2e', 'playwright', 'node_modules', 'dist', '.idea', '.git', '.cache'],
    browser: {
      enabled: false,
    },
  },
})
