<!doctype html>
<!-- HTML5 doctype declaration -->
<html lang="en">
  <!-- Root HTML element with language set to English -->
  <head>
    <!-- Head: metadata, styles, scripts -->
    <meta charset="UTF-8" />
    <!-- Character encoding for Unicode support -->
    <link rel="icon" type="image/png" href="/favicon.png" />
    <!-- Favicon shown in browser tab -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
    <!-- Viewport for responsive layout on mobile -->
    <link rel="canonical" href="https://cogbridges.com/" />
    <!-- Canonical URL for SEO -->
    <meta name="description" content="Get trustworthy answers from real Reddit discussions — AI-curated with traceable citations and commenter background analysis." />
    <!-- SEO description -->
    <meta name="robots" content="index,follow" />
    <meta name="theme-color" content="#0ea5e9" />
    
    <!-- Open Graph -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://cogbridges.com/" />
    <meta property="og:site_name" content="CogBridges" />
    <meta property="og:title" content="CogBridges — Trustworthy answers from real Reddit" />
    <meta property="og:description" content="AI-curated answers from real Reddit discussions, with traceable citations and commenter background analysis." />
    <meta property="og:image" content="https://cogbridges.com/og-image.png" />
    
    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="CogBridges — Trustworthy answers from real Reddit" />
    <meta name="twitter:description" content="AI-curated answers with citations and commenter background analysis" />
    <meta name="twitter:image" content="https://cogbridges.com/og-image.png" />
    
    <!-- iOS favicon -->
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    
    <title>CogBridges — Trustworthy answers from real Reddit</title>
    <!-- Page title shown in the browser tab -->
  </head>
  <body>
    <!-- App root -->
    <div id="root"></div>
    <!-- React mount point -->
    <script type="module" src="/src/main.jsx"></script>
    <!-- Entry script using ES modules -->
  </body>
</html>
