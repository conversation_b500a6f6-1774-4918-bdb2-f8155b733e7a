# 评论相关性排序功能更新

## 概述
本次更新为评论筛选系统添加了基于相关性的排序功能，确保与查询最相关的评论优先展示给用户。

## 主要更改

### 1. LLM 提示词优化
- 更新了 `ContentSelectionService` 中的 system prompt 和 user prompt
- 明确要求 LLM 按相关性分数从高到低返回评论列表
- 添加了 schema 描述，强调返回数组必须按相关性排序

### 2. 内容筛选服务改进
- 修改了评论处理逻辑，严格保持 LLM 返回的顺序
- 为每条评论添加 `_relevance_score` 属性
- 移除了后续的重新排序，避免破坏 LLM 的排序结果

### 3. 数据库支持
- 在 `RedditComment` 模型中添加了 `relevance_score` 字段（FLOAT 类型）
- 更新了数据保存逻辑，将相关性分数持久化到数据库
- 修改了前端数据加载逻辑，优先使用包含相关性分数的完整数据

### 4. 工具函数更新
- 更新了 `prune_posts_to_top_n_comments` 函数
- 在选择 top N 评论时优先考虑相关性分数
- 保持每个帖子内评论的原始顺序（LLM 排序后的顺序）

### 5. 测试覆盖
- 添加了 `test_content_selection_preserves_llm_order` 测试
- 添加了 `test_prune_posts_to_top_n_comments_with_relevance_score` 测试
- 所有测试均已通过

## 数据库迁移
已成功执行数据库迁移，为 `reddit_comments` 表添加了 `relevance_score` 列：
- 类型：DOUBLE PRECISION
- 可为空：True
- 默认值：0.0

## 影响
- 用户现在能够优先看到与其查询最相关的评论
- 提升了搜索结果的质量和用户体验
- 历史搜索结果也能正确显示相关性排序（如果有相关数据）