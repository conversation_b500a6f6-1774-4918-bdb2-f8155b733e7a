import { HashRouter as Router, Routes, Route, useLocation, useNavigate } from 'react-router-dom'
import HomePage from './pages/HomePage'
import LoadingPage from './pages/LoadingPage'
import ResultsPage from './pages/ResultsPage'
import HistoryPage from './pages/HistoryPage'
import TestPage from './pages/TestPage'
import LoginPage from './pages/LoginPage'
import RegisterPage from './pages/RegisterPage'
import PayPalSuccessPage from './pages/PayPalSuccessPage'
import PaymentCancelPage from './pages/PaymentCancelPage'
import { useEffect } from 'react'
import { getActiveSearch, isSearchInProgress } from './services/searchSession'

function SearchRecovery() {
  const location = useLocation()
  const navigate = useNavigate()
  useEffect(() => {
    try {
      const active = getActiveSearch()
      if (active && isSearchInProgress(active.status)) {
        if (location.pathname !== '/loading') {
          navigate('/loading', { state: { query: active.query, sessionId: active.clientSessionId }, replace: true })
        }
      }
    } catch (_) {}
  }, [location.pathname, navigate])
  return null
}

function App() {
  return (
    <Router>
      <div>
        <SearchRecovery />
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/loading" element={<LoadingPage />} />
          <Route path="/results" element={<ResultsPage />} />
          <Route path="/history" element={<HistoryPage />} />
          <Route path="/test" element={<TestPage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<RegisterPage />} />
          <Route path="/payment/success" element={<PayPalSuccessPage />} />
          <Route path="/payment/cancel" element={<PaymentCancelPage />} />
        </Routes>
      </div>
    </Router>
  )
}

// 导出App组件，供其他文件使用
export default App
