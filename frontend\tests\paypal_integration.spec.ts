import { test, expect } from '@playwright/test'

test.describe('PayPal Payment Integration', () => {
  test.beforeEach(async ({ page }) => {
    // Mock PayPal SDK
    await page.addInitScript(() => {
      window.paypal = {
        Buttons: () => ({
          render: () => {}
        })
      };
    });
  });

  test('should display PayPal purchase modal', async ({ page }) => {
    await page.goto('/')
    
    // Wait for page to load
    await expect(page.getByRole('img', { name: 'CogBridges' })).toBeVisible()
    
    // Mock authentication
    await page.evaluate(() => {
      localStorage.setItem('auth_token', 'test_token')
      window.authStore = {
        token: 'test_token',
        authenticated: true,
        user: { email: '<EMAIL>' }
      }
    })
    
    // Reload to apply auth state
    await page.reload()
    
    // Look for purchase button or points balance
    const pointsBalance = page.locator('[data-testid="points-balance"]').or(
      page.getByText('Purchase Points')
    ).or(
      page.getByText('Buy Points')
    ).first()
    
    if (await pointsBalance.isVisible()) {
      await pointsBalance.click()
    } else {
      // If no direct purchase button, try to trigger insufficient points modal
      const searchInput = page.getByRole('textbox', { name: 'Search questions from Reddit' })
      await searchInput.fill('test search')
      await page.getByRole('button', { name: 'Submit search' }).click()
      
      // Wait for insufficient points modal
      await expect(page.getByText('Insufficient Points')).toBeVisible({ timeout: 10000 })
      await page.getByText('Purchase Points').click()
    }
    
    // Verify PayPal purchase modal is displayed
    await expect(page.getByText('Purchase Points')).toBeVisible()
    await expect(page.getByText('$4.50')).toBeVisible()
    await expect(page.getByText('$7.50')).toBeVisible()
    await expect(page.getByText('$15.00')).toBeVisible()
    await expect(page.getByText('Most Popular')).toBeVisible()
    await expect(page.getByText('Payment processed securely by PayPal')).toBeVisible()
  })

  test('should show PayPal payment buttons', async ({ page }) => {
    await page.goto('/')
    
    // Mock authentication and open purchase modal
    await page.evaluate(() => {
      localStorage.setItem('auth_token', 'test_token')
      window.authStore = {
        token: 'test_token',
        authenticated: true,
        user: { email: '<EMAIL>' }
      }
    })
    
    await page.reload()
    
    // Open purchase modal (try different methods)
    try {
      await page.getByText('Purchase Points').click()
    } catch {
      try {
        await page.getByText('Buy Points').click()
      } catch {
        // Trigger through search if no direct button
        const searchInput = page.getByRole('textbox', { name: 'Search questions from Reddit' })
        await searchInput.fill('test search')
        await page.getByRole('button', { name: 'Submit search' }).click()
        await page.getByText('Purchase Points').click()
      }
    }
    
    // Wait for modal to appear
    await expect(page.getByText('Purchase Points')).toBeVisible()
    
    // Check for PayPal buttons (they might be loading initially)
    await expect(
      page.getByText('Loading PayPal...').or(
        page.getByText('Pay with PayPal')
      ).first()
    ).toBeVisible({ timeout: 10000 })
  })

  test('should handle PayPal payment flow', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/paypal/create-order', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          payment_id: 'PAY-TEST123',
          approval_url: 'https://www.paypal.com/approve/PAY-TEST123'
        })
      })
    })
    
    await page.goto('/')
    
    // Mock authentication
    await page.evaluate(() => {
      localStorage.setItem('auth_token', 'test_token')
      window.authStore = {
        token: 'test_token',
        authenticated: true,
        user: { email: '<EMAIL>' }
      }
    })
    
    await page.reload()
    
    // Open purchase modal
    try {
      await page.getByText('Purchase Points').click()
    } catch {
      const searchInput = page.getByRole('textbox', { name: 'Search questions from Reddit' })
      await searchInput.fill('test search')
      await page.getByRole('button', { name: 'Submit search' }).click()
      await page.getByText('Purchase Points').click()
    }
    
    // Wait for modal and PayPal buttons
    await expect(page.getByText('Purchase Points')).toBeVisible()
    
    // Mock PayPal button click
    await page.evaluate(() => {
      // Simulate PayPal button functionality
      const buttons = document.querySelectorAll('button')
      buttons.forEach(button => {
        if (button.textContent?.includes('Pay with PayPal')) {
          button.onclick = () => {
            // Simulate redirect to PayPal
            window.location.href = 'https://www.paypal.com/approve/PAY-TEST123'
          }
        }
      })
    })
    
    // Click PayPal button if available
    const paypalButton = page.getByText('Pay with PayPal').first()
    if (await paypalButton.isVisible()) {
      await paypalButton.click()
      
      // Should redirect to PayPal (in real scenario)
      // For testing, we just verify the API call was made
      await page.waitForRequest('**/api/paypal/create-order')
    }
  })

  test('should handle PayPal payment success', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/paypal/execute-payment', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          points_added: 500,
          new_balance: 500
        })
      })
    })
    
    await page.route('**/api/user/points', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          balance: 500
        })
      })
    })
    
    // Navigate to PayPal success page with mock parameters
    await page.goto('/payment/success?paymentId=PAY-TEST123&PayerID=PAYER123')
    
    // Should show processing initially
    await expect(page.getByText('Processing Payment...')).toBeVisible()
    
    // Should show success after API call
    await expect(page.getByText('Payment Successful!')).toBeVisible({ timeout: 10000 })
    await expect(page.getByText('+500 points added to your account')).toBeVisible()
    
    // Should have return to home button
    await expect(page.getByText('Return to Home')).toBeVisible()
  })

  test('should handle PayPal payment error', async ({ page }) => {
    // Mock API error response
    await page.route('**/api/paypal/execute-payment', async route => {
      await route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          error: 'Payment processing failed'
        })
      })
    })
    
    // Navigate to PayPal success page with mock parameters
    await page.goto('/payment/success?paymentId=PAY-TEST123&PayerID=PAYER123')
    
    // Should show error after API call
    await expect(page.getByText('Payment Failed')).toBeVisible({ timeout: 10000 })
    await expect(page.getByText('Payment processing failed')).toBeVisible()
    
    // Should have try again button
    await expect(page.getByText('Try Again')).toBeVisible()
  })

  test('should handle missing payment parameters', async ({ page }) => {
    // Navigate to PayPal success page without parameters
    await page.goto('/payment/success')
    
    // Should show error for missing parameters
    await expect(page.getByText('Payment Failed')).toBeVisible({ timeout: 10000 })
    await expect(page.getByText('Missing payment information')).toBeVisible()
  })
})
