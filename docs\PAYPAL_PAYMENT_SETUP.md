# PayPal 付费功能设置指南

## 功能概述

本系统实现了基于点数的付费模式，使用PayPal作为支付处理器：
- 每次深度搜索消耗 **20点**
- 未登录用户只能试用 **1次**（基于Cookie，终身限制）
- 注册用户获得 **40点**（约2次搜索，注册必须验证邮箱）
- 用完免费额度必须付费购买
- 点数永不过期

## 价格方案

| 套餐 | 价格 | 点数 | 搜索次数 |
|------|------|------|----------|
| 基础 | $4.50   | 250  | 约12次   |
| 热门 | $7.50   | 500  | 约25次   |
| 超值 | $15.00  | 1000 | 约50次   |

## 环境配置

### 1. 创建 `.env` 文件

```bash
cp .env.example .env
```

### 2. 配置 PayPal 相关变量

```env
# PayPal Configuration
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_MODE=sandbox  # sandbox for testing, live for production
PAYPAL_WEBHOOK_ID=your-paypal-webhook-id

# Frontend URL (for redirect)
FRONTEND_URL=http://localhost:5173
```

### 3. 在 PayPal Developer Dashboard 创建应用

1. 访问 [PayPal Developer Dashboard](https://developer.paypal.com/)
2. 登录并创建新应用
3. 选择 "Default Application" 或创建新应用
4. 获取 Client ID 和 Client Secret
5. 配置 Return URLs:
   - Success URL: `https://your-domain.com/payment/success`
   - Cancel URL: `https://your-domain.com/payment/cancel`

### 4. 配置 Webhook (可选)

1. 在 PayPal Developer Dashboard 进入 "Webhooks"
2. 点击 "Create Webhook"
3. 设置 Webhook URL: `https://your-domain.com/api/paypal/webhook`
4. 选择事件类型: `PAYMENT.SALE.COMPLETED`
5. 记录 Webhook ID

## 前端配置

配置前端环境变量：

```bash
# 前端 .env
VITE_PAYPAL_CLIENT_ID=your-paypal-client-id
VITE_API_URL=http://localhost:5000
```

## API 接口

### 1. 创建 PayPal 订单

```
POST /api/paypal/create-order
{
  "plan_id": "500"
}
```

响应：
```json
{
  "success": true,
  "payment_id": "PAY-123456789",
  "approval_url": "https://www.paypal.com/approve/PAY-123456789"
}
```

### 2. 执行 PayPal 支付

```
POST /api/paypal/execute-payment
{
  "payment_id": "PAY-123456789",
  "payer_id": "PAYER123"
}
```

响应：
```json
{
  "success": true,
  "points_added": 500,
  "new_balance": 500
}
```

### 3. 获取点数余额

```
GET /api/user/points
```

### 4. 获取点数历史

```
GET /api/user/points/history
```

### 5. PayPal Webhook

```
POST /api/paypal/webhook
```

## 业务流程

### 注册流程
1. 用户填写邮箱密码
2. 发送验证码到邮箱
3. 输入验证码完成注册
4. 注册成功直接赠送40点
5. 记录到 `points_ledger` 表

### 搜索流程
1. 检查用户余额是否 ≥ 20点
2. 如果不足，返回错误提示充值
3. 搜索前验证，搜索成功后扣点
4. 记录扣点到 `points_ledger` 表

### 充值流程
1. 用户选择套餐 → 创建 PayPal 订单
2. 跳转到 PayPal 支付页面
3. 用户完成支付 → 返回成功页面
4. 执行支付 → 增加用户余额
5. 记录到 `points_ledger` 表（幂等性）

## 测试

### 使用 PayPal 沙盒测试

1. 在 PayPal Developer Dashboard 创建沙盒账户
2. 使用沙盒买家账户进行测试
3. 设置 `PAYPAL_MODE=sandbox`

### 测试账户

PayPal 提供测试用的买家和卖家账户：
- 买家账户：用于模拟用户购买
- 卖家账户：用于接收测试支付

## 生产部署

### 1. 切换到生产模式

```env
PAYPAL_MODE=live
PAYPAL_CLIENT_ID=your-live-client-id
PAYPAL_CLIENT_SECRET=your-live-client-secret
```

### 2. 更新前端配置

```env
VITE_PAYPAL_CLIENT_ID=your-live-client-id
VITE_API_URL=https://your-api-domain.com
```

### 3. 配置 HTTPS

确保所有 PayPal 相关的 URL 都使用 HTTPS。

## 安全注意事项

1. **永远不要在前端暴露 PayPal Client Secret**
2. **使用 HTTPS** 进行所有 PayPal 通信
3. **验证 Webhook 签名**（生产环境推荐）
4. **实施幂等性检查** 防止重复支付
5. **记录所有支付事务** 用于审计

## 故障排除

### 常见问题

1. **支付失败**
   - 检查 PayPal 配置是否正确
   - 确认网络连接
   - 查看服务器日志

2. **重定向失败**
   - 检查 Return URLs 配置
   - 确认前端路由设置

3. **点数未增加**
   - 检查支付执行逻辑
   - 查看数据库事务日志
   - 确认幂等性处理

### 日志监控

监控以下日志：
- PayPal API 调用
- 支付执行结果
- 数据库事务
- 用户点数变化

## 支持

如需帮助，请查看：
- [PayPal Developer Documentation](https://developer.paypal.com/docs/)
- [PayPal REST API Reference](https://developer.paypal.com/docs/api/)
- 项目 GitHub Issues
