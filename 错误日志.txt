services.grok_reddit_service - INFO - 使用Grok模型: grok-3-mini
2025-08-24 13:04:08 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-08-24 13:04:08 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: 碰到了难以修复的bug怎么办
2025-08-24 13:04:08 - services.cogbridges_service - INFO - 步骤1: Grok 搜索(仅搜索) - 碰到了难以修复的bug怎么办
2025-08-24 13:04:08 - services.grok_reddit_service - INFO - 开始 Grok 搜索(仅搜索=True): 碰到了难以修复的bug怎么办
10.229.227.65 - - [24/Aug/2025:13:04:11 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 179 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.228.20.182 - - [24/Aug/2025:13:04:11 +0000] "GET /api/health HTTP/1.1" 200 91 "-" "Render/1.0"
10.229.118.132 - - [24/Aug/2025:13:04:13 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 179 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.229.118.132 - - [24/Aug/2025:13:04:15 +0000] "OPTIONS /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 0 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.229.227.65 - - [24/Aug/2025:13:04:15 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 179 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.228.20.182 - - [24/Aug/2025:13:04:16 +0000] "GET /api/health HTTP/1.1" 200 91 "-" "Render/1.0"
10.229.217.197 - - [24/Aug/2025:13:04:18 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 180 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.229.118.132 - - [24/Aug/2025:13:04:20 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 179 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.228.20.182 - - [24/Aug/2025:13:04:21 +0000] "GET /api/health HTTP/1.1" 200 92 "-" "Render/1.0"
10.229.118.132 - - [24/Aug/2025:13:04:22 +0000] "OPTIONS /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 0 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.229.21.133 - - [24/Aug/2025:13:04:22 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 180 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.229.227.65 - - [24/Aug/2025:13:04:25 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 180 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
2025-08-24 13:04:25 - services.grok_reddit_service - INFO - 获取到 17 个搜索引用
2025-08-24 13:04:25 - services.grok_reddit_service - INFO -   引用 1: https://reddit.com/r/learnprogramming/comments/11045gh - N/A
2025-08-24 13:04:25 - services.grok_reddit_service - INFO -   引用 2: https://reddit.com/r/learnprogramming/comments/qbactl - N/A
2025-08-24 13:04:25 - services.grok_reddit_service - INFO -   引用 3: https://reddit.com/r/bugs/comments/14yui4d - N/A
2025-08-24 13:04:25 - services.grok_reddit_service - INFO -   引用 4: https://reddit.com/r/bugs/comments/l6h7ci - N/A
2025-08-24 13:04:25 - services.grok_reddit_service - INFO -   引用 5: https://reddit.com/r/learnprogramming/comments/13j7m4y - N/A
2025-08-24 13:04:25 - services.grok_reddit_service - INFO -   引用 6: https://reddit.com/r/qualityassurance/comments/1fvj3wm - N/A
2025-08-24 13:04:25 - services.grok_reddit_service - INFO -   引用 7: https://reddit.com/r/blog/comments/qxqdoq - N/A
2025-08-24 13:04:25 - services.grok_reddit_service - INFO -   引用 8: https://reddit.com/r/help/comments/1dmisuj - N/A
2025-08-24 13:04:25 - services.grok_reddit_service - INFO -   引用 9: https://reddit.com/r/help/comments/152578k - N/A
2025-08-24 13:04:25 - services.grok_reddit_service - INFO -   引用 10: https://reddit.com/r/help/comments/17w685x - N/A
2025-08-24 13:04:25 - services.grok_reddit_service - INFO -   引用 11: https://reddit.com/r/learnprogramming/comments/u72ycj - N/A
2025-08-24 13:04:25 - services.grok_reddit_service - INFO -   引用 12: https://reddit.com/r/degoogle/comments/106fru1 - N/A
2025-08-24 13:04:25 - services.grok_reddit_service - INFO -   引用 13: https://reddit.com/r/beta/comments/1525j71 - N/A
2025-08-24 13:04:25 - services.grok_reddit_service - INFO -   引用 14: https://reddit.com/r/help/comments/1e5hrku - N/A
2025-08-24 13:04:25 - services.grok_reddit_service - INFO -   引用 15: https://reddit.com/r/help/comments/1e9idd1 - N/A
2025-08-24 13:04:25 - services.grok_reddit_service - INFO -   引用 16: https://reddit.com/r/help/comments/13jardt - N/A
2025-08-24 13:04:25 - services.grok_reddit_service - INFO -   引用 17: https://reddit.com/r/bugs/comments/6sshah - N/A
2025-08-24 13:04:25 - services.grok_reddit_service - INFO - 已禁用IO快照保存（ENABLE_IO_SNAPSHOTS=False）
2025-08-24 13:04:25 - services.grok_reddit_service - INFO - Grok Reddit搜索（仅搜索）成功，耗时: 17.33秒，引用数: 17
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API call to https://www.reddit.com/api/v1/access_token
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/1e5hrku/: 998 remaining
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/13j7m4y/: 996 remaining
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/13j7m4y/: 995 remaining
/opt/render/project/src/services/reddit_service.py:262: UserWarning: The comments for this submission have already been fetched, so the updated comment_sort will not have any effect.
  submission.comment_sort = 'top'
2025-08-24 13:04:26 - services.reddit_service - INFO - 获取评论成功: 3 条评论
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/1dmisuj/: 983 remaining
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/14yui4d/: 991 remaining
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/1e9idd1/: 982 remaining
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/6sshah/: 997 remaining
2025-08-24 13:04:26 - services.reddit_service - INFO - 获取评论成功: 3 条评论
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/14yui4d/: 980 remaining
2025-08-24 13:04:26 - services.reddit_service - INFO - 获取评论成功: 3 条评论
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/17w685x/: 987 remaining
2025-08-24 13:04:26 - services.reddit_service - INFO - 获取评论成功: 2 条评论
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/1dmisuj/: 968 remaining
2025-08-24 13:04:26 - services.reddit_service - INFO - 获取评论成功: 0 条评论
10.228.20.182 - - [24/Aug/2025:13:04:26 +0000] "GET /api/health HTTP/1.1" 200 92 "-" "Render/1.0"
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/1e5hrku/: 972 remaining
2025-08-24 13:04:26 - services.reddit_service - INFO - 获取评论成功: 2 条评论
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/17w685x/: 978 remaining
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/1e9idd1/: 973 remaining
2025-08-24 13:04:26 - services.reddit_service - INFO - 获取评论成功: 5 条评论
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/1525j71/: 989 remaining
2025-08-24 13:04:26 - services.reddit_service - INFO - 获取评论成功: 15 条评论
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/152578k/: 993 remaining
2025-08-24 13:04:26 - services.reddit_service - INFO - 获取评论成功: 18 条评论
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/1525j71/: 988 remaining
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/qbactl/: 967 remaining
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/qbactl/: 966 remaining
2025-08-24 13:04:26 - services.reddit_service - INFO - 获取评论成功: 13 条评论
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/6sshah/: 990 remaining
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/1fvj3wm/: 976 remaining
2025-08-24 13:04:26 - services.reddit_service - INFO - 获取评论成功: 28 条评论
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/13jardt/: 979 remaining
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/1fvj3wm/: 977 remaining
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/13jardt/: 986 remaining
2025-08-24 13:04:26 - services.reddit_service - INFO - 获取评论成功: 55 条评论
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/152578k/: 981 remaining
2025-08-24 13:04:26 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/l6h7ci/: 985 remaining
2025-08-24 13:04:27 - services.reddit_service - INFO - 获取评论成功: 57 条评论
2025-08-24 13:04:27 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/l6h7ci/: 994 remaining
2025-08-24 13:04:27 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/11045gh/: 975 remaining
2025-08-24 13:04:27 - services.reddit_service - INFO - 获取评论成功: 96 条评论
2025-08-24 13:04:27 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/11045gh/: 971 remaining
2025-08-24 13:04:27 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/qxqdoq/: 974 remaining
2025-08-24 13:04:27 - services.reddit_service - INFO - 获取评论成功: 80 条评论
2025-08-24 13:04:27 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/qxqdoq/: 970 remaining
2025-08-24 13:04:27 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/u72ycj/: 992 remaining
2025-08-24 13:04:27 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/u72ycj/: 999 remaining
2025-08-24 13:04:27 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/106fru1/: 984 remaining
2025-08-24 13:04:27 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/comments/106fru1/: 969 remaining
2025-08-24 13:04:27 - services.reddit_service - INFO - 获取评论成功: 100 条评论
2025-08-24 13:04:27 - services.reddit_service - INFO - 获取评论成功: 100 条评论
2025-08-24 13:04:28 - services.content_selection_service - INFO - 内容筛选输入条目: 总数 580，送入LLM 580，目标TopN 15
2025-08-24 13:04:28 - services.llm_service - INFO - Starting Replicate API call: POST run
10.229.217.197 - - [24/Aug/2025:13:04:28 +0000] "OPTIONS /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 0 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.229.118.132 - - [24/Aug/2025:13:04:28 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 180 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.229.205.131 - - [24/Aug/2025:13:04:31 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 180 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.228.20.182 - - [24/Aug/2025:13:04:31 +0000] "GET /api/health HTTP/1.1" 200 92 "-" "Render/1.0"
10.229.205.131 - - [24/Aug/2025:13:04:33 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 180 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.229.217.197 - - [24/Aug/2025:13:04:35 +0000] "OPTIONS /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 0 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.229.227.65 - - [24/Aug/2025:13:04:35 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 179 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.228.20.182 - - [24/Aug/2025:13:04:36 +0000] "GET /api/health HTTP/1.1" 200 91 "-" "Render/1.0"
10.228.20.182 - - [24/Aug/2025:13:04:36 +0000] "GET /api/health HTTP/1.1" 200 92 "-" "Render/1.0"
10.229.118.132 - - [24/Aug/2025:13:04:38 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 179 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.229.21.133 - - [24/Aug/2025:13:04:40 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 179 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.228.20.182 - - [24/Aug/2025:13:04:41 +0000] "GET /api/health HTTP/1.1" 200 92 "-" "Render/1.0"
10.229.21.133 - - [24/Aug/2025:13:04:43 +0000] "OPTIONS /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 0 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.229.118.132 - - [24/Aug/2025:13:04:43 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 180 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.229.205.131 - - [24/Aug/2025:13:04:46 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 179 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.228.20.182 - - [24/Aug/2025:13:04:46 +0000] "GET /api/health HTTP/1.1" 200 92 "-" "Render/1.0"
2025-08-24 13:04:46 - services.llm_service - INFO - LLM调用成功，输出长度: 3312
2025-08-24 13:04:46 - services.llm_service - INFO - Replicate API call successful, duration: 18.88s
2025-08-24 13:04:46 - services.cogbridges_service - INFO - 步骤1完成: citations 抓取并构建 3 个帖子，耗时: 38.35秒
2025-08-24 13:04:46 - services.cogbridges_service - INFO - 步骤2: 使用新的overview方法获取评论者历史数据
2025-08-24 13:04:46 - services.cogbridges_service - INFO - 需要获取 8 个评论者的历史数据 (原始: 8)
2025-08-24 13:04:46 - services.cogbridges_service - INFO - 步骤2批次获取：单次尝试，用户数 8
2025-08-24 13:04:47 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/user/lurgi/: 963 remaining
10.229.118.132 - - [24/Aug/2025:13:04:48 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 179 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
2025-08-24 13:04:48 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/user/HappyRogue121/: 958 remaining
2025-08-24 13:04:48 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/user/SeeJaneCode/: 961 remaining
2025-08-24 13:04:48 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/user/errorkode/: 964 remaining
2025-08-24 13:04:48 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/user/desrtfx/: 960 remaining
2025-08-24 13:04:48 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/user/insertAlias/: 965 remaining
2025-08-24 13:04:48 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/user/puggsincyberspace/: 962 remaining
2025-08-24 13:04:48 - reddit.ratelimit - INFO - Reddit API rate limit for https://oauth.reddit.com/user/notislant/: 959 remaining
2025-08-24 13:04:48 - services.cogbridges_service - INFO - 步骤2完成: 获取了 8 个用户的历史数据, 单次尝试, 耗时: 1.80秒
2025-08-24 13:04:48 - services.cogbridges_service - INFO - 步骤3: 开始LLM分析...
2025-08-24 13:04:48 - services.cogbridges_service - INFO - 提取到 8 个用户数据
2025-08-24 13:04:48 - services.cogbridges_service - INFO - 执行评论者背景分析...
2025-08-24 13:04:48 - services.cogbridges_service - INFO - 背景分析：使用单用户并发路径，用户数=8，最大并发=999999
2025-08-24 13:04:48 - services.cogbridges_service - INFO - 并发任务开始: 用户 puggsincyberspace
2025-08-24 13:04:48 - services.llm_service - INFO - 开始分析用户puggsincyberspace的背景 - 高赞内容数量: 20
2025-08-24 13:04:48 - services.llm_service - INFO - Starting Replicate API call: POST run
2025-08-24 13:04:48 - services.cogbridges_service - INFO - 并发任务开始: 用户 errorkode
2025-08-24 13:04:48 - services.llm_service - INFO - 开始分析用户errorkode的背景 - 高赞内容数量: 20
2025-08-24 13:04:48 - services.llm_service - INFO - Starting Replicate API call: POST run
2025-08-24 13:04:48 - services.cogbridges_service - INFO - 并发任务开始: 用户 desrtfx
2025-08-24 13:04:48 - services.llm_service - INFO - 开始分析用户desrtfx的背景 - 高赞内容数量: 20
2025-08-24 13:04:48 - services.llm_service - INFO - Starting Replicate API call: POST run
2025-08-24 13:04:48 - services.cogbridges_service - INFO - 并发任务开始: 用户 HappyRogue121
2025-08-24 13:04:48 - services.llm_service - INFO - 开始分析用户HappyRogue121的背景 - 高赞内容数量: 20
2025-08-24 13:04:48 - services.llm_service - INFO - Starting Replicate API call: POST run
2025-08-24 13:04:48 - services.cogbridges_service - INFO - 并发任务开始: 用户 lurgi
2025-08-24 13:04:48 - services.llm_service - INFO - 开始分析用户lurgi的背景 - 高赞内容数量: 20
2025-08-24 13:04:48 - services.llm_service - INFO - Starting Replicate API call: POST run
2025-08-24 13:04:48 - services.cogbridges_service - INFO - 并发任务开始: 用户 SeeJaneCode
2025-08-24 13:04:48 - services.llm_service - INFO - 开始分析用户SeeJaneCode的背景 - 高赞内容数量: 20
2025-08-24 13:04:48 - services.llm_service - INFO - Starting Replicate API call: POST run
2025-08-24 13:04:48 - services.cogbridges_service - INFO - 并发任务开始: 用户 notislant
2025-08-24 13:04:48 - services.llm_service - INFO - 开始分析用户notislant的背景 - 高赞内容数量: 20
2025-08-24 13:04:48 - services.llm_service - INFO - Starting Replicate API call: POST run
2025-08-24 13:04:48 - services.cogbridges_service - INFO - 并发任务开始: 用户 insertAlias
2025-08-24 13:04:48 - services.llm_service - INFO - 开始分析用户insertAlias的背景 - 高赞内容数量: 20
2025-08-24 13:04:48 - services.llm_service - INFO - Starting Replicate API call: POST run
10.229.118.132 - - [24/Aug/2025:13:04:50 +0000] "OPTIONS /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 0 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.229.118.132 - - [24/Aug/2025:13:04:50 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 180 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.228.20.182 - - [24/Aug/2025:13:04:51 +0000] "GET /api/health HTTP/1.1" 200 92 "-" "Render/1.0"
10.229.10.130 - - [24/Aug/2025:13:04:53 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 179 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.229.21.133 - - [24/Aug/2025:13:04:55 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 179 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.228.20.182 - - [24/Aug/2025:13:04:56 +0000] "GET /api/health HTTP/1.1" 200 91 "-" "Render/1.0"
10.229.205.131 - - [24/Aug/2025:13:04:58 +0000] "OPTIONS /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 0 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.229.118.132 - - [24/Aug/2025:13:04:58 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 180 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.229.118.132 - - [24/Aug/2025:13:05:01 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 180 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.228.20.182 - - [24/Aug/2025:13:05:01 +0000] "GET /api/health HTTP/1.1" 200 91 "-" "Render/1.0"
2025-08-24 13:05:02 - services.llm_service - INFO - LLM调用成功，输出长度: 4295
2025-08-24 13:05:02 - services.llm_service - INFO - Replicate API call successful, duration: 14.00s
2025-08-24 13:05:02 - services.llm_service - INFO - 用户HappyRogue121背景分析完成
2025-08-24 13:05:02 - services.cogbridges_service - INFO - 并发任务完成: 用户 HappyRogue121，用时 14.00s
2025-08-24 13:05:02 - services.cogbridges_service - INFO - 增量结果已到达: HappyRogue121
10.229.227.65 - - [24/Aug/2025:13:05:03 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 180 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
2025-08-24 13:05:04 - services.llm_service - INFO - LLM调用成功，输出长度: 4273
2025-08-24 13:05:04 - services.llm_service - INFO - Replicate API call successful, duration: 15.74s
2025-08-24 13:05:04 - services.llm_service - INFO - 用户notislant背景分析完成
2025-08-24 13:05:04 - services.cogbridges_service - INFO - 并发任务完成: 用户 notislant，用时 15.74s
2025-08-24 13:05:04 - services.cogbridges_service - INFO - 增量结果已到达: notislant
2025-08-24 13:05:04 - services.llm_service - INFO - LLM调用成功，输出长度: 4629
2025-08-24 13:05:04 - services.llm_service - INFO - Replicate API call successful, duration: 16.03s
2025-08-24 13:05:04 - services.llm_service - INFO - 用户errorkode背景分析完成
2025-08-24 13:05:04 - services.cogbridges_service - INFO - 并发任务完成: 用户 errorkode，用时 16.03s
2025-08-24 13:05:04 - services.cogbridges_service - INFO - 增量结果已到达: errorkode
10.229.217.197 - - [24/Aug/2025:13:05:05 +0000] "OPTIONS /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 0 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.229.118.132 - - [24/Aug/2025:13:05:05 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 179 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
2025-08-24 13:05:05 - services.llm_service - INFO - LLM调用成功，输出长度: 4119
2025-08-24 13:05:05 - services.llm_service - INFO - Replicate API call successful, duration: 16.99s
2025-08-24 13:05:05 - services.llm_service - ERROR - LLM响应不是有效的JSON格式: {
  "expertise": {
    "summary": "Experienced professional software developer with practical debugging, mentoring, and teachability insights; secondary hands-on hobbies include aquariums, mechanical keyboards, and pet ownership.",
    "evidence_comments": [
      "I've been a professional developer for 16 years now. _Every job_ I've had, I've been issued Windows mach...",
      "Believe it or not, creating a high-quality tutorial is _hard_. Not everyone is the greatest teacher or lesson planner...",
      "Fun story: not the hardest per se, but definitely one of the oddest and most frustrating. A junior coworker of mine was having some trouble debugging an issue...",
      "My Blue Acaras had babies",
      "A coworker who knows I'm into mechs gave me the strangest \"keyboard\" I've ever seen: an \"Alphagrip\".",
      "Meet Ruby"
    ]
  },
  "background_similarity": {
    "summary": "Likely a mid-career professional developer who mentors juniors, moderates online communities, and maintains hobbyist interests (fishkeeping, mechanical keyboards, owning a dog).",
    "possible_tags": [
      "mid-career software engineer",
      "mentor/team lead",
      "community moderator",
      "pet owner (dog, aquarium)",
      "mechanical keyboard enthusiast"
    ],
    "evidence_comments": [
      "I've been a professional developer for 16 years now. _Every job_ I've had, I've been issued Windows mach...",
      "Stepping down as Moderator",
      "A coworker who knows I'm into mechs gave me the strangest \"keyboard\" I've ever seen: an \"Alphagrip\".",
      "My Blue Acaras had babies",
      "Meet Ruby",
      "A junior coworker of mine was having some trouble debugging an issue..."
    ]
  },
  "worldview": {
    "summary": "Pragmatic, mentorship-focused, and practical: values clear teaching, hands-on experience, and resilient problem-solving; skeptical of gatekeeping and of platitudes (e.g., 'just code' or security-through-obscurity).",
    "value_tags": [
      "pragmatism",
      "mentorship",
      "practical education",
      "anti-gatekeeping",
      "patience with learners",
      "skeptical of dogma"
    ],
    "evidence_comments": [
      "Believe it or not, creating a high-quality tutorial is _hard_. Not everyone is the greatest teacher or lesson planner...",
      "That sounds like an oversimplification of the common advice, which is: don't get stuck in the tutorial cyc...",
      \"It's not about security. It's about control.\",",
      "First: while not as common as some make it out to be, there are definitely some assholes in the profession. You'll just have to learn to roll with it and ignore their comments.",
      "A junior coworker of mine was having some trouble debugging an issue..."
    ]
  },
  "per_comment": {
    "/r/learnprogramming/comments/13j7m4y/what_is_the_hardest_bug_youve_had_to_find_and_fix/jkdpnhv/": {
      "comment_excerpt": "Fun story: not the hardest per se, but definitely one of the oddest and most frustrating. A junior coworker of mine was having some trouble debugging an issue. I don't even remember what the issue was, but while they were actively debugging the issue, it _always_ operated one way (and not the corre",
      "relevance_to_expertise": 9,
      "relevance_to_background": 8,
      "relevance_to_worldview": 7,
      "overall_relevance": 8,
      "comment_specific_background": {
        "summary": "Shows the author is a senior/experienced developer who works with juniors and participates in debugging/mentoring in a workplace.",
        "tags": [
          "senior developer",
          "mentor",
          "workplace debugging"
        ]
      },
      "justification": "The comment describes a debugging anecdote involving a 'junior coworker' and the author's role in diagnosing a strange issue — indicating hands-on professional experience, mentorship, and familiarity with debugging workflows.",
      "evidence_spans": [
        "A junior coworker of mine was having some trouble debugging an issue.",
        "while they were actively debugging the issue, it _always_ operated one way"
      ]
    }
  }
}
2025-08-24 13:05:05 - services.llm_service - INFO - 用户insertAlias背景分析完成
2025-08-24 13:05:05 - services.cogbridges_service - INFO - 并发任务完成: 用户 insertAlias，用时 16.99s
2025-08-24 13:05:05 - services.cogbridges_service - INFO - 增量结果已到达: insertAlias
2025-08-24 13:05:06 - services.llm_service - INFO - LLM调用成功，输出长度: 4881
2025-08-24 13:05:06 - services.llm_service - INFO - Replicate API call successful, duration: 17.27s
2025-08-24 13:05:06 - services.llm_service - INFO - 用户SeeJaneCode背景分析完成
2025-08-24 13:05:06 - services.cogbridges_service - INFO - 并发任务完成: 用户 SeeJaneCode，用时 17.27s
2025-08-24 13:05:06 - services.cogbridges_service - INFO - 增量结果已到达: SeeJaneCode
10.228.20.182 - - [24/Aug/2025:13:05:06 +0000] "GET /api/health HTTP/1.1" 200 92 "-" "Render/1.0"
10.228.20.182 - - [24/Aug/2025:13:05:06 +0000] "GET /api/health HTTP/1.1" 200 92 "-" "Render/1.0"
2025-08-24 13:05:07 - services.llm_service - INFO - LLM调用成功，输出长度: 4321
2025-08-24 13:05:07 - services.llm_service - INFO - Replicate API call successful, duration: 18.27s
2025-08-24 13:05:07 - services.llm_service - INFO - 用户desrtfx背景分析完成
2025-08-24 13:05:07 - services.cogbridges_service - INFO - 并发任务完成: 用户 desrtfx，用时 18.27s
2025-08-24 13:05:07 - services.cogbridges_service - INFO - 增量结果已到达: desrtfx
10.229.118.132 - - [24/Aug/2025:13:05:08 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 179 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
2025-08-24 13:05:08 - services.llm_service - INFO - LLM调用成功，输出长度: 4599
2025-08-24 13:05:08 - services.llm_service - INFO - Replicate API call successful, duration: 19.54s
2025-08-24 13:05:08 - services.llm_service - INFO - 用户puggsincyberspace背景分析完成
2025-08-24 13:05:08 - services.cogbridges_service - INFO - 并发任务完成: 用户 puggsincyberspace，用时 19.54s
2025-08-24 13:05:08 - services.cogbridges_service - INFO - 增量结果已到达: puggsincyberspace
2025-08-24 13:05:08 - services.llm_service - INFO - LLM调用成功，输出长度: 4099
2025-08-24 13:05:08 - services.llm_service - INFO - Replicate API call successful, duration: 19.77s
2025-08-24 13:05:08 - services.llm_service - INFO - 用户lurgi背景分析完成
2025-08-24 13:05:08 - services.cogbridges_service - INFO - 并发任务完成: 用户 lurgi，用时 19.77s
2025-08-24 13:05:08 - services.cogbridges_service - INFO - 增量结果已到达: lurgi
2025-08-24 13:05:08 - services.cogbridges_service - INFO - 步骤3完成: LLM分析完成, 耗时: 19.77秒
2025-08-24 13:05:08 - services.cogbridges_service - INFO - CogBridges搜索完成: 碰到了难以修复的bug怎么办, 总耗时: 59.92秒
2025-08-24 13:05:08 - api.app - INFO - Starting async save of search results to database: search_1756040647098_iywhd6
2025-08-24 13:05:08 - api.blueprints_search - INFO - User 3 charged 20 points (comments=8  e= 5)
2025-08-24 13:05:08 - services.grok_reddit_service - INFO - Grok Reddit服务已关闭
2025-08-24 13:05:08 - services.cogbridges_service - INFO - CogBridges服务已关闭
2025-08-24 13:05:08 - services.database_service - INFO - 数据库连接池已清理
2025-08-24 13:05:08 - services.data_service - INFO - 数据服务资源已清理
2025-08-24 13:05:08 - services.database_service - INFO - 数据库连接池已清理
2025-08-24 13:05:08 - services.database_service - ERROR - 数据库操作失败: (psycopg2.errors.ForeignKeyViolation) insert or update on table "reddit_posts" violates foreign key constraint "reddit_posts_session_id_fkey"
DETAIL:  Key (session_id)=(search_1756040647098_iywhd6) is not present in table "search_sessions".
[SQL: INSERT INTO reddit_posts (id, session_id, title, selftext, author, score, num_comments, created_utc, subreddit, permalink, url, created_at) VALUES (%(id__0)s, %(session_id__0)s, %(title__0)s, %(selftext__0)s, %(author__0)s, %(score__0)s, %(num_commen ... 435 characters truncated ... _2)s, %(url__2)s, now()) RETURNING reddit_posts.created_at, reddit_posts.id, reddit_posts.session_id]
[parameters: {'session_id__0': 'search_1756040647098_iywhd6', 'title__0': "what do you do when you've cant solve a bug?", 'score__0': 266, 'num_comments__0': 163, 'subreddit__0': 'learnprogramming', 'url__0': 'https://reddit.com/r/learnprogramming/comments/11045gh', 'author__0': '[deleted]', 'created_utc__0': 1676166832.0, 'selftext__0': '[deleted]', 'permalink__0': '/r/learnprogramming/comments/11045gh', 'id__0': '11045gh', 'session_id__1': 'search_1756040647098_iywhd6', 'title__1': 'how are bugs fixed?', 'score__1': 24, 'num_comments__1': 23, 'subreddit__1': 'learnprogramming', 'url__1': 'https://reddit.com/r/learnprogramming/comments/qbactl', 'author__1': 'kemkomkinomi', 'created_utc__1': 1634646082.0, 'selftext__1': 'sorry, im a newbie in computer programming, and have always  wonder how are bugs fixed', 'permalink__1': '/r/learnprogramming/comments/qbactl', 'id__1': 'qbactl', 'session_id__2': 'search_1756040647098_iywhd6', 'title__2': "What is the hardest bug you've had to find and fix?", 'score__2': 0, 'num_comments__2': 3, 'subreddit__2': 'learnprogramming', 'url__2': 'https://reddit.com/r/learnprogramming/comments/13j7m4y', 'author__2': 'Fair-Item-561', 'created_utc__2': 1684249176.0, 'selftext__2': 'Over the years, this has been my most effective technical interview question.', 'permalink__2': '/r/learnprogramming/comments/13j7m4y', 'id__2': '13j7m4y'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-24 13:05:08 - services.database_service - ERROR - 保存搜索会话数据失败: (psycopg2.errors.ForeignKeyViolation) insert or update on table "reddit_posts" violates foreign key constraint "reddit_posts_session_id_fkey"
DETAIL:  Key (session_id)=(search_1756040647098_iywhd6) is not present in table "search_sessions".
[SQL: INSERT INTO reddit_posts (id, session_id, title, selftext, author, score, num_comments, created_utc, subreddit, permalink, url, created_at) VALUES (%(id__0)s, %(session_id__0)s, %(title__0)s, %(selftext__0)s, %(author__0)s, %(score__0)s, %(num_commen ... 435 characters truncated ... _2)s, %(url__2)s, now()) RETURNING reddit_posts.created_at, reddit_posts.id, reddit_posts.session_id]
[parameters: {'session_id__0': 'search_1756040647098_iywhd6', 'title__0': "what do you do when you've cant solve a bug?", 'score__0': 266, 'num_comments__0': 163, 'subreddit__0': 'learnprogramming', 'url__0': 'https://reddit.com/r/learnprogramming/comments/11045gh', 'author__0': '[deleted]', 'created_utc__0': 1676166832.0, 'selftext__0': '[deleted]', 'permalink__0': '/r/learnprogramming/comments/11045gh', 'id__0': '11045gh', 'session_id__1': 'search_1756040647098_iywhd6', 'title__1': 'how are bugs fixed?', 'score__1': 24, 'num_comments__1': 23, 'subreddit__1': 'learnprogramming', 'url__1': 'https://reddit.com/r/learnprogramming/comments/qbactl', 'author__1': 'kemkomkinomi', 'created_utc__1': 1634646082.0, 'selftext__1': 'sorry, im a newbie in computer programming, and have always  wonder how are bugs fixed', 'permalink__1': '/r/learnprogramming/comments/qbactl', 'id__1': 'qbactl', 'session_id__2': 'search_1756040647098_iywhd6', 'title__2': "What is the hardest bug you've had to find and fix?", 'score__2': 0, 'num_comments__2': 3, 'subreddit__2': 'learnprogramming', 'url__2': 'https://reddit.com/r/learnprogramming/comments/13j7m4y', 'author__2': 'Fair-Item-561', 'created_utc__2': 1684249176.0, 'selftext__2': 'Over the years, this has been my most effective technical interview question.', 'permalink__2': '/r/learnprogramming/comments/13j7m4y', 'id__2': '13j7m4y'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-24 13:05:08 - services.cogbridges_service - ERROR - 保存搜索结果失败: 会话数据保存到数据库失败
2025-08-24 13:05:08 - services.cogbridges_service - INFO - 搜索结果异步保存成功: search_1756040647098_iywhd6
2025-08-24 13:05:08 - api.app - INFO - Search results saved asynchronously: search_1756040647098_iywhd6
10.229.227.65 - - [24/Aug/2025:13:05:10 +0000] "GET /api/search/progress/search_1756040647098_iywhd6 HTTP/1.1" 200 204173 "https://www.cogbridges.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
10.228.20.182 - - [24/Aug/2025:13:05:11 +0000] "GET /api/health HTTP/1.1" 200 92 "-" "Render/1.0"